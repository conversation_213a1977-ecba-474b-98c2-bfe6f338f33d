const fs = require("fs");
const path = require("path");
const FormData = require("form-data");
const axios = require("axios");

// Create a simple test image (1x1 pixel PNG)
const createTestImage = () => {
  // Base64 encoded 1x1 pixel transparent PNG
  const base64PNG = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==";
  const buffer = Buffer.from(base64PNG, "base64");
  const testImagePath = path.join(__dirname, "test-profile.png");
  fs.writeFileSync(testImagePath, buffer);
  return testImagePath;
};

// Test the complete image upload flow
async function testCompleteImageUpload() {
  try {
    console.log("🧪 Testing Complete Image Upload Flow...\n");

    const baseURL = "http://localhost:5000";
    let authToken = null;
    let userId = null;

    // Step 1: Create test image
    console.log("1. Creating test image...");
    const testImagePath = createTestImage();
    console.log("✅ Test image created:", testImagePath);

    // Step 2: Register a test user (you'll need to create a tenant first)
    console.log("\n2. Attempting to register a test user...");
    const testUser = {
      email: `test-${Date.now()}@example.com`,
      password: "TestPassword123!",
      full_name: "Test User",
      mobile_number: "+1234567890",
      usn: `TEST${Date.now()}`,
      course_name: "Computer Science",
      batch_year: 2024,
      role: "STUDENT",
      tenant_id: 1, // Assuming tenant with ID 1 exists
    };

    try {
      const registerResponse = await axios.post(`${baseURL}/api/auth/register`, testUser);
      console.log("✅ User registered successfully");
      userId = registerResponse.data.user.id;
    } catch (error) {
      if (error.response) {
        console.log("❌ Registration failed:", error.response.data.message || error.response.data.error);
        console.log("   This might be because:");
        console.log("   - No tenant exists with ID 1");
        console.log("   - Database is not properly set up");
        console.log("   - User already exists");

        // Try to login instead
        console.log("\n   Attempting to login with existing credentials...");
        try {
          const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
            email: "<EMAIL>", // Try with a default admin
            password: "admin123",
          });
          authToken = loginResponse.data.accessToken;
          userId = loginResponse.data.user.id;
          console.log("✅ Logged in with existing user");
        } catch (loginError) {
          console.log("❌ Login also failed. Skipping authenticated tests.");
          console.log("   Please ensure you have a user account set up in the database.");
          return;
        }
      } else {
        throw error;
      }
    }

    // Step 3: Login to get auth token (if not already obtained)
    if (!authToken) {
      console.log("\n3. Logging in to get auth token...");
      try {
        const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
          email: testUser.email,
          password: testUser.password,
        });
        authToken = loginResponse.data.accessToken;
        console.log("✅ Login successful, token obtained");
      } catch (error) {
        console.log("❌ Login failed:", error.response?.data?.message || error.message);
        return;
      }
    }

    // Step 4: Test profile picture upload
    console.log("\n4. Testing profile picture upload...");
    const form = new FormData();
    form.append("profilePicture", fs.createReadStream(testImagePath));

    try {
      const uploadResponse = await axios.post(`${baseURL}/api/users/profile/picture`, form, {
        headers: {
          ...form.getHeaders(),
          Authorization: `Bearer ${authToken}`,
        },
      });

      console.log("✅ Profile picture uploaded successfully!");
      console.log("   Response:", uploadResponse.data.message);
      console.log("   Image URL:", uploadResponse.data.profilePicture.url);

      // Step 5: Test if uploaded image is accessible
      const imageUrl = uploadResponse.data.profilePicture.url;
      console.log("\n5. Testing if uploaded image is accessible...");

      try {
        const imageResponse = await axios.get(`${baseURL}${imageUrl}`);
        console.log("✅ Uploaded image is accessible via static serving");
        console.log("   Content-Type:", imageResponse.headers["content-type"]);
        console.log("   Content-Length:", imageResponse.headers["content-length"]);
      } catch (error) {
        console.log("❌ Uploaded image is not accessible:", error.message);
      }

      // Step 6: Test uploading another image (should replace the old one)
      console.log("\n6. Testing image replacement...");
      const form2 = new FormData();
      form2.append("profilePicture", fs.createReadStream(testImagePath));

      const uploadResponse2 = await axios.post(`${baseURL}/api/users/profile/picture`, form2, {
        headers: {
          ...form2.getHeaders(),
          Authorization: `Bearer ${authToken}`,
        },
      });

      console.log("✅ Profile picture replaced successfully!");
      console.log("   New Image URL:", uploadResponse2.data.profilePicture.url);

      // Verify old image URL is different
      if (uploadResponse.data.profilePicture.url !== uploadResponse2.data.profilePicture.url) {
        console.log("✅ Old image was properly replaced with new one");
      }
    } catch (error) {
      console.log("❌ Profile picture upload failed:", error.response?.data?.message || error.message);
      if (error.response?.data) {
        console.log("   Error details:", error.response.data);
      }
    }

    // Cleanup
    console.log("\n7. Cleaning up test files...");
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
      console.log("✅ Test image cleaned up");
    }

    console.log("\n🎉 Complete image upload flow test completed!");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", JSON.stringify(error.response.data, null, 2));
    }
    console.error("Full error:", error);
  }
}

// Run the test
testCompleteImageUpload();
