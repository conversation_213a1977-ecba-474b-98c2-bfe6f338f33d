"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.socketService = void 0;
const client_1 = require("@prisma/client");
const socket_1 = require("../config/socket");
const socket_2 = require("../types/socket");
const loggerService_1 = require("./loggerService");
const prisma = new client_1.PrismaClient();
class SocketService {
    constructor() {
        this.io = null;
        this.onlineUsers = new Map();
        this.userSockets = new Map();
        this.typingUsers = new Map();
    }
    initialize(io) {
        this.io = io;
        loggerService_1.Logger.info("SocketService initialized");
    }
    getIO() {
        if (!this.io) {
            this.io = (0, socket_1.getSocketIO)();
        }
        return this.io;
    }
    async handleUserConnection(socket) {
        if (!socket.userId || !socket.tenantId)
            return;
        try {
            const user = await prisma.user.findUnique({
                where: { id: socket.userId },
                select: {
                    id: true,
                    full_name: true,
                    tenant_id: true,
                },
            });
            if (!user)
                return;
            const onlineUser = {
                userId: user.id,
                tenantId: user.tenant_id,
                fullName: user.full_name,
                status: socket_2.UserStatus.ONLINE,
                lastSeen: new Date(),
                socketId: socket.id,
            };
            this.onlineUsers.set(user.id, onlineUser);
            if (!this.userSockets.has(user.id)) {
                this.userSockets.set(user.id, new Set());
            }
            this.userSockets.get(user.id).add(socket.id);
            await socket.join([
                socket_1.SOCKET_ROOMS.USER(user.id),
                socket_1.SOCKET_ROOMS.TENANT(user.tenant_id),
                socket_1.SOCKET_ROOMS.NOTIFICATIONS(user.id),
                socket_1.SOCKET_ROOMS.ONLINE_USERS(user.tenant_id),
            ]);
            socket.to(socket_1.SOCKET_ROOMS.TENANT(user.tenant_id)).emit(socket_1.SOCKET_EVENTS.USER_ONLINE, {
                userId: user.id,
                fullName: user.full_name,
                status: socket_2.UserStatus.ONLINE,
                timestamp: new Date(),
            });
            const tenantOnlineUsers = Array.from(this.onlineUsers.values()).filter((u) => u.tenantId === user.tenant_id);
            socket.emit(socket_1.SOCKET_EVENTS.ONLINE_USERS_LIST, tenantOnlineUsers);
            loggerService_1.Logger.info(`User ${user.id} (${user.full_name}) connected via socket ${socket.id}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error handling user connection:", error);
        }
    }
    async handleUserDisconnection(socket) {
        if (!socket.userId || !socket.tenantId)
            return;
        try {
            const userId = socket.userId;
            const tenantId = socket.tenantId;
            const userSocketSet = this.userSockets.get(userId);
            if (userSocketSet) {
                userSocketSet.delete(socket.id);
                if (userSocketSet.size === 0) {
                    this.userSockets.delete(userId);
                    const onlineUser = this.onlineUsers.get(userId);
                    if (onlineUser) {
                        onlineUser.status = socket_2.UserStatus.OFFLINE;
                        onlineUser.lastSeen = new Date();
                        socket.to(socket_1.SOCKET_ROOMS.TENANT(tenantId)).emit(socket_1.SOCKET_EVENTS.USER_OFFLINE, {
                            userId,
                            status: socket_2.UserStatus.OFFLINE,
                            lastSeen: onlineUser.lastSeen,
                            timestamp: new Date(),
                        });
                        setTimeout(() => {
                            const currentSocketSet = this.userSockets.get(userId);
                            if (!currentSocketSet || currentSocketSet.size === 0) {
                                this.onlineUsers.delete(userId);
                            }
                        }, 30000);
                    }
                }
            }
            loggerService_1.Logger.info(`User ${userId} disconnected from socket ${socket.id}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error handling user disconnection:", error);
        }
    }
    async sendNotificationToUser(userId, notification) {
        try {
            const io = this.getIO();
            io.to(socket_1.SOCKET_ROOMS.NOTIFICATIONS(userId)).emit(socket_1.SOCKET_EVENTS.RECEIVE_NOTIFICATION, notification);
            const unreadCount = await this.getUnreadNotificationCount(userId);
            io.to(socket_1.SOCKET_ROOMS.USER(userId)).emit(socket_1.SOCKET_EVENTS.NOTIFICATION_COUNT, { count: unreadCount });
            loggerService_1.Logger.info(`Notification sent to user ${userId}:`, notification.type);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending notification to user:", error);
        }
    }
    async sendNotificationToUsers(userIds, notification) {
        const promises = userIds.map((userId) => this.sendNotificationToUser(userId, { ...notification, userId }));
        await Promise.all(promises);
    }
    async sendNotificationToTenant(tenantId, notification) {
        try {
            const io = this.getIO();
            io.to(socket_1.SOCKET_ROOMS.TENANT(tenantId)).emit(socket_1.SOCKET_EVENTS.RECEIVE_NOTIFICATION, notification);
            loggerService_1.Logger.info(`Notification sent to tenant ${tenantId}:`, notification.type);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending notification to tenant:", error);
        }
    }
    async sendPrivateMessage(senderId, receiverId, message) {
        try {
            const io = this.getIO();
            const roomId = socket_1.SOCKET_ROOMS.PRIVATE_CHAT(senderId, receiverId);
            io.to(socket_1.SOCKET_ROOMS.USER(senderId)).emit(socket_1.SOCKET_EVENTS.RECEIVE_MESSAGE, message);
            io.to(socket_1.SOCKET_ROOMS.USER(receiverId)).emit(socket_1.SOCKET_EVENTS.RECEIVE_MESSAGE, message);
            if (this.onlineUsers.has(receiverId)) {
                io.to(socket_1.SOCKET_ROOMS.USER(senderId)).emit(socket_1.SOCKET_EVENTS.MESSAGE_DELIVERED, {
                    messageId: message.id,
                    receiverId,
                    deliveredAt: new Date(),
                });
            }
            loggerService_1.Logger.info(`Private message sent from ${senderId} to ${receiverId}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending private message:", error);
        }
    }
    handleTypingIndicator(indicator) {
        try {
            const io = this.getIO();
            if (indicator.receiverId) {
                io.to(socket_1.SOCKET_ROOMS.USER(indicator.receiverId)).emit(socket_1.SOCKET_EVENTS.TYPING_INDICATOR, indicator);
            }
            else if (indicator.roomId) {
                io.to(indicator.roomId).emit(socket_1.SOCKET_EVENTS.TYPING_INDICATOR, indicator);
            }
        }
        catch (error) {
            loggerService_1.Logger.error("Error handling typing indicator:", error);
        }
    }
    async handleFollowRequest(followRequest) {
        try {
            const notification = {
                userId: followRequest.followingId,
                tenantId: followRequest.tenantId,
                type: "follow_request",
                title: "New Follow Request",
                message: `${followRequest.followerName} wants to follow you`,
                data: { followRequest },
                createdAt: new Date(),
            };
            await this.sendNotificationToUser(followRequest.followingId, notification);
            const io = this.getIO();
            io.to(socket_1.SOCKET_ROOMS.USER(followRequest.followingId)).emit(socket_1.SOCKET_EVENTS.FOLLOW_REQUEST, followRequest);
            loggerService_1.Logger.info(`Follow request sent from ${followRequest.followerId} to ${followRequest.followingId}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error handling follow request:", error);
        }
    }
    async handleEventUpdate(eventUpdate) {
        try {
            const io = this.getIO();
            io.to(socket_1.SOCKET_ROOMS.TENANT(eventUpdate.tenantId)).emit(socket_1.SOCKET_EVENTS.EVENT_UPDATED, eventUpdate);
            io.to(socket_1.SOCKET_ROOMS.EVENT(eventUpdate.eventId)).emit(socket_1.SOCKET_EVENTS.EVENT_UPDATED, eventUpdate);
            loggerService_1.Logger.info(`Event update broadcasted for event ${eventUpdate.eventId}:`, eventUpdate.updateType);
        }
        catch (error) {
            loggerService_1.Logger.error("Error handling event update:", error);
        }
    }
    async handleJobUpdate(jobUpdate) {
        try {
            const io = this.getIO();
            io.to(socket_1.SOCKET_ROOMS.TENANT(jobUpdate.tenantId)).emit(socket_1.SOCKET_EVENTS.JOB_UPDATED, jobUpdate);
            io.to(socket_1.SOCKET_ROOMS.JOB(jobUpdate.jobId)).emit(socket_1.SOCKET_EVENTS.JOB_UPDATED, jobUpdate);
            loggerService_1.Logger.info(`Job update broadcasted for job ${jobUpdate.jobId}:`, jobUpdate.updateType);
        }
        catch (error) {
            loggerService_1.Logger.error("Error handling job update:", error);
        }
    }
    async handlePostUpdate(postUpdate) {
        try {
            const io = this.getIO();
            io.to(socket_1.SOCKET_ROOMS.TENANT(postUpdate.tenantId)).emit(socket_1.SOCKET_EVENTS.POST_UPDATED, postUpdate);
            io.to(socket_1.SOCKET_ROOMS.POST(postUpdate.postId)).emit(socket_1.SOCKET_EVENTS.POST_UPDATED, postUpdate);
            loggerService_1.Logger.info(`Post update broadcasted for post ${postUpdate.postId}:`, postUpdate.updateType);
        }
        catch (error) {
            loggerService_1.Logger.error("Error handling post update:", error);
        }
    }
    getOnlineUsersForTenant(tenantId) {
        return Array.from(this.onlineUsers.values()).filter((user) => user.tenantId === tenantId);
    }
    isUserOnline(userId) {
        return this.onlineUsers.has(userId);
    }
    getUserSocketIds(userId) {
        const socketSet = this.userSockets.get(userId);
        return socketSet ? Array.from(socketSet) : [];
    }
    async getUnreadNotificationCount(userId) {
        try {
            return 0;
        }
        catch (error) {
            loggerService_1.Logger.error("Error getting unread notification count:", error);
            return 0;
        }
    }
    async updateUserStatus(userId, status) {
        try {
            const onlineUser = this.onlineUsers.get(userId);
            if (onlineUser) {
                onlineUser.status = status;
                onlineUser.lastSeen = new Date();
                const io = this.getIO();
                io.to(socket_1.SOCKET_ROOMS.TENANT(onlineUser.tenantId)).emit(socket_1.SOCKET_EVENTS.USER_STATUS_CHANGE, {
                    userId,
                    status,
                    lastSeen: onlineUser.lastSeen,
                    timestamp: new Date(),
                });
                loggerService_1.Logger.info(`User ${userId} status updated to ${status}`);
            }
        }
        catch (error) {
            loggerService_1.Logger.error("Error updating user status:", error);
        }
    }
    async joinRoom(socket, roomId, roomType) {
        try {
            await socket.join(roomId);
            if (roomType === "chat" || roomType === "event") {
                socket.to(roomId).emit(socket_1.SOCKET_EVENTS.USER_ONLINE, {
                    userId: socket.userId,
                    roomId,
                    timestamp: new Date(),
                });
            }
            loggerService_1.Logger.info(`User ${socket.userId} joined room ${roomId} (${roomType})`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error joining room:", error);
        }
    }
    async leaveRoom(socket, roomId, roomType) {
        try {
            await socket.leave(roomId);
            if (roomType === "chat" || roomType === "event") {
                socket.to(roomId).emit(socket_1.SOCKET_EVENTS.USER_OFFLINE, {
                    userId: socket.userId,
                    roomId,
                    timestamp: new Date(),
                });
            }
            loggerService_1.Logger.info(`User ${socket.userId} left room ${roomId} (${roomType})`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error leaving room:", error);
        }
    }
    async broadcastToRoom(roomId, event, data, excludeUserId) {
        try {
            const io = this.getIO();
            if (excludeUserId) {
                const userSocketIds = this.getUserSocketIds(excludeUserId);
                const sockets = await io.in(roomId).fetchSockets();
                sockets.forEach((socket) => {
                    if (!userSocketIds.includes(socket.id)) {
                        socket.emit(event, data);
                    }
                });
            }
            else {
                io.to(roomId).emit(event, data);
            }
            loggerService_1.Logger.info(`Broadcasted ${event} to room ${roomId}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error broadcasting to room:", error);
        }
    }
    async getRoomMembers(roomId) {
        try {
            const io = this.getIO();
            const sockets = await io.in(roomId).fetchSockets();
            const userIds = [];
            sockets.forEach((socket) => {
                const authSocket = socket;
                if (authSocket.userId) {
                    userIds.push(authSocket.userId);
                }
            });
            return [...new Set(userIds)];
        }
        catch (error) {
            loggerService_1.Logger.error("Error getting room members:", error);
            return [];
        }
    }
    cleanup() {
        this.onlineUsers.clear();
        this.userSockets.clear();
        this.typingUsers.clear();
        loggerService_1.Logger.info("SocketService cleaned up");
    }
}
exports.socketService = new SocketService();
exports.default = exports.socketService;
//# sourceMappingURL=socketService.js.map