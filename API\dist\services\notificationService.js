"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationService = void 0;
const client_1 = require("@prisma/client");
const socketService_1 = require("./socketService");
const loggerService_1 = require("./loggerService");
const socket_1 = require("../types/socket");
const prisma = new client_1.PrismaClient();
class NotificationService {
    async sendFollowRequestNotification(followerId, followingId, tenantId) {
        try {
            const follower = await prisma.user.findUnique({
                where: { id: followerId },
                select: { full_name: true },
            });
            if (!follower)
                return;
            const notification = {
                userId: followingId,
                tenantId,
                type: socket_1.NotificationType.FOLLOW_REQUEST,
                title: "New Follow Request",
                message: `${follower.full_name} wants to follow you`,
                data: { followerId, followerName: follower.full_name },
                createdAt: new Date(),
            };
            await socketService_1.socketService.sendNotificationToUser(followingId, notification);
            loggerService_1.Logger.info(`Follow request notification sent from ${followerId} to ${followingId}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending follow request notification:", error);
        }
    }
    async sendFollowAcceptedNotification(followerId, followingId, tenantId) {
        try {
            const following = await prisma.user.findUnique({
                where: { id: followingId },
                select: { full_name: true },
            });
            if (!following)
                return;
            const notification = {
                userId: followerId,
                tenantId,
                type: socket_1.NotificationType.FOLLOW_ACCEPTED,
                title: "Follow Request Accepted",
                message: `${following.full_name} accepted your follow request`,
                data: { followingId, followingName: following.full_name },
                createdAt: new Date(),
            };
            await socketService_1.socketService.sendNotificationToUser(followerId, notification);
            loggerService_1.Logger.info(`Follow accepted notification sent to ${followerId} from ${followingId}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending follow accepted notification:", error);
        }
    }
    async sendConnectionRequestNotification(requesterId, targetId, tenantId) {
        try {
            const requester = await prisma.user.findUnique({
                where: { id: requesterId },
                select: { full_name: true },
            });
            if (!requester)
                return;
            const notification = {
                userId: targetId,
                tenantId,
                type: socket_1.NotificationType.CONNECTION_REQUEST,
                title: "New Connection Request",
                message: `${requester.full_name} wants to connect with you`,
                data: { requesterId, requesterName: requester.full_name },
                createdAt: new Date(),
            };
            await socketService_1.socketService.sendNotificationToUser(targetId, notification);
            loggerService_1.Logger.info(`Connection request notification sent from ${requesterId} to ${targetId}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending connection request notification:", error);
        }
    }
    async sendConnectionAcceptedNotification(requesterId, accepterId, tenantId) {
        try {
            const accepter = await prisma.user.findUnique({
                where: { id: accepterId },
                select: { full_name: true },
            });
            if (!accepter)
                return;
            const notification = {
                userId: requesterId,
                tenantId,
                type: socket_1.NotificationType.CONNECTION_ACCEPTED,
                title: "Connection Request Accepted",
                message: `${accepter.full_name} accepted your connection request`,
                data: { accepterId, accepterName: accepter.full_name },
                createdAt: new Date(),
            };
            await socketService_1.socketService.sendNotificationToUser(requesterId, notification);
            loggerService_1.Logger.info(`Connection accepted notification sent to ${requesterId} from ${accepterId}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending connection accepted notification:", error);
        }
    }
    async sendMessageNotification(senderId, receiverId, tenantId, messageContent) {
        try {
            const sender = await prisma.user.findUnique({
                where: { id: senderId },
                select: { full_name: true },
            });
            if (!sender)
                return;
            const notification = {
                userId: receiverId,
                tenantId,
                type: socket_1.NotificationType.MESSAGE,
                title: "New Message",
                message: `${sender.full_name}: ${messageContent.substring(0, 50)}${messageContent.length > 50 ? "..." : ""}`,
                data: { senderId, senderName: sender.full_name, messageContent },
                createdAt: new Date(),
            };
            await socketService_1.socketService.sendNotificationToUser(receiverId, notification);
            loggerService_1.Logger.info(`Message notification sent from ${senderId} to ${receiverId}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending message notification:", error);
        }
    }
    async sendEventNotification(eventId, tenantId, type, authorId) {
        try {
            const event = await prisma.event.findUnique({
                where: { id: eventId },
                include: { author: { select: { full_name: true } } },
            });
            if (!event)
                return;
            let title = "";
            let message = "";
            switch (type) {
                case socket_1.EventUpdateType.CREATED:
                    title = "New Event Created";
                    message = `${event.author.full_name} created a new event: ${event.title}`;
                    break;
                case socket_1.EventUpdateType.UPDATED:
                    title = "Event Updated";
                    message = `${event.author.full_name} updated the event: ${event.title}`;
                    break;
                case socket_1.EventUpdateType.DELETED:
                    title = "Event Cancelled";
                    message = `${event.author.full_name} cancelled the event: ${event.title}`;
                    break;
                default:
                    return;
            }
            const notification = {
                userId: 0,
                tenantId,
                type: socket_1.NotificationType.EVENT_INVITATION,
                title,
                message,
                data: { eventId, eventTitle: event.title, authorName: event.author.full_name },
                createdAt: new Date(),
            };
            const tenantUsers = await prisma.user.findMany({
                where: {
                    tenant_id: tenantId,
                    account_status: "APPROVED",
                    id: { not: authorId },
                },
                select: { id: true },
            });
            const userIds = tenantUsers.map(user => user.id);
            await socketService_1.socketService.sendNotificationToUsers(userIds, notification);
            const eventUpdate = {
                eventId,
                tenantId,
                authorId,
                title: event.title,
                description: event.description,
                startTime: event.start_time,
                endTime: event.end_time,
                location: event.location,
                updateType: type,
                updatedBy: event.author.full_name,
            };
            await socketService_1.socketService.handleEventUpdate(eventUpdate);
            loggerService_1.Logger.info(`Event notification sent for event ${eventId} (${type})`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending event notification:", error);
        }
    }
    async sendJobNotification(jobId, tenantId, type, authorId) {
        try {
            const job = await prisma.job.findUnique({
                where: { id: jobId },
                include: { author: { select: { full_name: true } } },
            });
            if (!job)
                return;
            let title = "";
            let message = "";
            switch (type) {
                case socket_1.JobUpdateType.POSTED:
                    title = "New Job Posted";
                    message = `${job.author.full_name} posted a new job: ${job.title} at ${job.company_name}`;
                    break;
                case socket_1.JobUpdateType.UPDATED:
                    title = "Job Updated";
                    message = `${job.author.full_name} updated the job: ${job.title}`;
                    break;
                case socket_1.JobUpdateType.DELETED:
                    title = "Job Removed";
                    message = `${job.author.full_name} removed the job: ${job.title}`;
                    break;
                default:
                    return;
            }
            const notification = {
                userId: 0,
                tenantId,
                type: socket_1.NotificationType.JOB_POSTED,
                title,
                message,
                data: { jobId, jobTitle: job.title, companyName: job.company_name, authorName: job.author.full_name },
                createdAt: new Date(),
            };
            const tenantUsers = await prisma.user.findMany({
                where: {
                    tenant_id: tenantId,
                    account_status: "APPROVED",
                    id: { not: authorId },
                },
                select: { id: true },
            });
            const userIds = tenantUsers.map(user => user.id);
            await socketService_1.socketService.sendNotificationToUsers(userIds, notification);
            const jobUpdate = {
                jobId,
                tenantId,
                authorId,
                title: job.title,
                companyName: job.company_name,
                updateType: type,
                updatedBy: job.author.full_name,
            };
            await socketService_1.socketService.handleJobUpdate(jobUpdate);
            loggerService_1.Logger.info(`Job notification sent for job ${jobId} (${type})`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending job notification:", error);
        }
    }
    async sendPostNotification(postId, tenantId, type, authorId, targetUserId) {
        try {
            const post = await prisma.generalPost.findUnique({
                where: { id: postId },
                include: { author: { select: { full_name: true } } },
            });
            if (!post)
                return;
            let title = "";
            let message = "";
            switch (type) {
                case socket_1.PostUpdateType.LIKED:
                    title = "Post Liked";
                    message = `${post.author.full_name} liked your post`;
                    break;
                case socket_1.PostUpdateType.COMMENTED:
                    title = "New Comment";
                    message = `${post.author.full_name} commented on your post`;
                    break;
                default:
                    return;
            }
            if (targetUserId && targetUserId !== authorId) {
                const notification = {
                    userId: targetUserId,
                    tenantId,
                    type: type === socket_1.PostUpdateType.LIKED ? socket_1.NotificationType.POST_LIKED : socket_1.NotificationType.POST_COMMENTED,
                    title,
                    message,
                    data: { postId, postTitle: post.title, authorName: post.author.full_name },
                    createdAt: new Date(),
                };
                await socketService_1.socketService.sendNotificationToUser(targetUserId, notification);
            }
            const postUpdate = {
                postId,
                tenantId,
                authorId,
                title: post.title,
                content: post.content,
                updateType: type,
                updatedBy: post.author.full_name,
            };
            await socketService_1.socketService.handlePostUpdate(postUpdate);
            loggerService_1.Logger.info(`Post notification sent for post ${postId} (${type})`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending post notification:", error);
        }
    }
    async sendSystemNotification(tenantId, title, message, data) {
        try {
            const notification = {
                userId: 0,
                tenantId,
                type: socket_1.NotificationType.SYSTEM,
                title,
                message,
                data,
                createdAt: new Date(),
            };
            await socketService_1.socketService.sendNotificationToTenant(tenantId, notification);
            loggerService_1.Logger.info(`System notification sent to tenant ${tenantId}: ${title}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending system notification:", error);
        }
    }
}
exports.notificationService = new NotificationService();
exports.default = exports.notificationService;
//# sourceMappingURL=notificationService.js.map