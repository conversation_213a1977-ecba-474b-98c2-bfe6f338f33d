"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.requireStudentOrAlumni = exports.requireAlumniOrAdmin = exports.requireStudent = exports.requireAlumni = exports.requireSuperAdmin = exports.requireTenantAdmin = exports.authorize = exports.requireApproved = exports.authenticate = void 0;
const client_1 = require("@prisma/client");
const auth_1 = require("../utils/auth");
const database_1 = require("../config/database");
const errorHandler_1 = require("./errorHandler");
const authenticate = async (req, res, next) => {
    try {
        console.log("🔍 Auth Debug - Authorization header:", req.headers.authorization);
        console.log("🔍 Auth Debug - All headers:", JSON.stringify(req.headers, null, 2));
        const token = auth_1.AuthUtils.extractTokenFromHeader(req.headers.authorization);
        console.log("🔍 Auth Debug - Extracted token:", token ? "Token found" : "No token");
        if (!token) {
            throw (0, errorHandler_1.createError)("Access token is required", 401);
        }
        const payload = auth_1.AuthUtils.verifyAccessToken(token);
        const user = await database_1.prisma.user.findUnique({
            where: { id: parseInt(payload.userId) },
            include: {
                tenant: {
                    select: {
                        is_active: true,
                    },
                },
            },
        });
        if (!user || !user.tenant.is_active) {
            throw (0, errorHandler_1.createError)("User not found or tenant inactive", 401);
        }
        if (user.account_status === client_1.UserStatus.DEACTIVATED) {
            throw (0, errorHandler_1.createError)("Account has been deactivated", 403);
        }
        if (user.account_status === client_1.UserStatus.REJECTED) {
            throw (0, errorHandler_1.createError)("Account access has been denied", 403);
        }
        req.user = {
            userId: payload.userId,
            email: user.email,
            role: user.role,
            status: user.account_status,
            id: user.id.toString(),
            tenant_id: user.tenant_id,
        };
        next();
    }
    catch (error) {
        if (error instanceof Error) {
            if (error.name === "JsonWebTokenError") {
                return next((0, errorHandler_1.createError)("Invalid token", 401));
            }
            if (error.name === "TokenExpiredError") {
                return next((0, errorHandler_1.createError)("Token expired", 401));
            }
        }
        next(error);
    }
};
exports.authenticate = authenticate;
const requireApproved = (req, res, next) => {
    if (!req.user) {
        return next((0, errorHandler_1.createError)("Authentication required", 401));
    }
    if (req.user.status !== client_1.UserStatus.APPROVED) {
        return next((0, errorHandler_1.createError)("Account pending approval", 403));
    }
    next();
};
exports.requireApproved = requireApproved;
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return next((0, errorHandler_1.createError)("Authentication required", 401));
        }
        if (!roles.includes(req.user.role)) {
            return next((0, errorHandler_1.createError)("Insufficient permissions", 403));
        }
        next();
    };
};
exports.authorize = authorize;
exports.requireTenantAdmin = (0, exports.authorize)(client_1.UserRole.TENANT_ADMIN, client_1.UserRole.SUPER_ADMIN);
exports.requireSuperAdmin = (0, exports.authorize)(client_1.UserRole.SUPER_ADMIN);
exports.requireAlumni = (0, exports.authorize)(client_1.UserRole.ALUMNUS);
exports.requireStudent = (0, exports.authorize)(client_1.UserRole.STUDENT);
exports.requireAlumniOrAdmin = (0, exports.authorize)(client_1.UserRole.ALUMNUS, client_1.UserRole.TENANT_ADMIN, client_1.UserRole.SUPER_ADMIN);
exports.requireStudentOrAlumni = (0, exports.authorize)(client_1.UserRole.STUDENT, client_1.UserRole.ALUMNUS);
const optionalAuth = async (req, res, next) => {
    try {
        const token = auth_1.AuthUtils.extractTokenFromHeader(req.headers.authorization);
        if (token) {
            const payload = auth_1.AuthUtils.verifyAccessToken(token);
            const user = await database_1.prisma.user.findUnique({
                where: { id: parseInt(payload.userId) },
                include: {
                    tenant: {
                        select: {
                            is_active: true,
                        },
                    },
                },
            });
            if (user && user.account_status === client_1.UserStatus.APPROVED && user.tenant.is_active) {
                req.user = {
                    userId: payload.userId,
                    email: user.email,
                    role: user.role,
                    status: user.account_status,
                    id: user.id.toString(),
                    tenant_id: user.tenant_id,
                };
            }
        }
    }
    catch (error) {
    }
    next();
};
exports.optionalAuth = optionalAuth;
//# sourceMappingURL=auth.js.map