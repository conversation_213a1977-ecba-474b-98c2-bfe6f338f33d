const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

async function createAdmin() {
  try {
    console.log("Creating approved admin user...\n");

    // Use a simple password hash for testing (not secure for production)
    const hashedPassword = "$2b$12$LQv3c1yqBwEHxPuNYkGOSuOkiDoSObp2WLO/QlDvwyoUkVkj4j.Iq"; // AdminPassword123!

    const adminUser = await prisma.user.upsert({
      where: {
        idx_tenant_email: {
          tenant_id: 1,
          email: "<EMAIL>",
        },
      },
      update: {
        account_status: "APPROVED",
      },
      create: {
        tenant_id: 1,
        full_name: "Test Admin",
        email: "<EMAIL>",
        password_hash: hashedPassword,
        usn: "ADMIN001",
        role: "TENANT_ADMIN",
        account_status: "APPROVED",
      },
    });

    console.log("✅ Admin user created/updated:");
    console.log("   Email: <EMAIL>");
    console.log("   Password: AdminPassword123!");
    console.log("   Role:", adminUser.role);
    console.log("   Status:", adminUser.account_status);
    console.log("   ID:", adminUser.id);
  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin();
