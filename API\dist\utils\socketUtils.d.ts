import { NotificationType } from "../types/socket";
export declare class SocketUtils {
    static triggerFollowRequest(followerId: number, followingId: number, tenantId: number): Promise<void>;
    static triggerFollowAccepted(followerId: number, followingId: number, tenantId: number): Promise<void>;
    static triggerConnectionRequest(requesterId: number, targetId: number, tenantId: number): Promise<void>;
    static triggerConnectionAccepted(requesterId: number, accepterId: number, tenantId: number): Promise<void>;
    static triggerMessageNotification(senderId: number, receiverId: number, tenantId: number, content: string): Promise<void>;
    static triggerEventCreated(eventId: number, tenantId: number, authorId: number): Promise<void>;
    static triggerEventUpdated(eventId: number, tenantId: number, authorId: number): Promise<void>;
    static triggerEventDeleted(eventId: number, tenantId: number, authorId: number): Promise<void>;
    static triggerJobPosted(jobId: number, tenantId: number, authorId: number): Promise<void>;
    static triggerJobUpdated(jobId: number, tenantId: number, authorId: number): Promise<void>;
    static triggerJobDeleted(jobId: number, tenantId: number, authorId: number): Promise<void>;
    static triggerPostLiked(postId: number, tenantId: number, likerId: number, postAuthorId: number): Promise<void>;
    static triggerPostCommented(postId: number, tenantId: number, commenterId: number, postAuthorId: number): Promise<void>;
    static triggerSystemNotification(tenantId: number, title: string, message: string, data?: any): Promise<void>;
    static getOnlineUsers(tenantId: number): import("../types/socket").OnlineUser[];
    static isUserOnline(userId: number): boolean;
    static sendCustomNotification(userId: number, tenantId: number, type: NotificationType, title: string, message: string, data?: any): Promise<void>;
    static sendCustomNotificationToUsers(userIds: number[], tenantId: number, type: NotificationType, title: string, message: string, data?: any): Promise<void>;
    static broadcastToTenant(tenantId: number, event: string, data: any): Promise<void>;
    static broadcastToUsers(userIds: number[], event: string, data: any): Promise<void>;
    static sendActivityUpdate(tenantId: number, activityType: string, userId: number, data?: any): Promise<void>;
    static getRoomMembers(roomId: string): Promise<number[]>;
    static forceDisconnectUser(userId: number, reason?: string): Promise<void>;
    static getServerStats(): {
        totalConnections: number;
        totalOnlineUsers: number;
        serverUptime: number;
        timestamp: Date;
    };
}
export default SocketUtils;
//# sourceMappingURL=socketUtils.d.ts.map