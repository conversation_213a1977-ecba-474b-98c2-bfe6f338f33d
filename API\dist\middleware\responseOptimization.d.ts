import { Request, Response, NextFunction } from "express";
export declare const fieldFiltering: () => (req: Request, res: Response, next: NextFunction) => void;
export declare const responseCompression: () => (req: Request, res: Response, next: NextFunction) => void;
export declare const responseTransformation: () => (req: Request, res: Response, next: NextFunction) => void;
export declare const cacheHeaders: (options?: {
    maxAge?: number;
    private?: boolean;
    noCache?: boolean;
    mustRevalidate?: boolean;
}) => (req: Request, res: Response, next: NextFunction) => void;
export declare const paginationOptimization: () => (req: Request, res: Response, next: NextFunction) => void;
declare global {
    namespace Express {
        interface Request {
            pagination?: {
                page: number;
                limit: number;
                offset: number;
            };
        }
    }
}
//# sourceMappingURL=responseOptimization.d.ts.map