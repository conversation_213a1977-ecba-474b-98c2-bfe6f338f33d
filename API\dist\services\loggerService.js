"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.morganStream = exports.Logger = void 0;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
};
const logColors = {
    error: "red",
    warn: "yellow",
    info: "green",
    http: "magenta",
    debug: "white",
};
winston_1.default.addColors(logColors);
const logsDir = path_1.default.join(process.cwd(), "logs");
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss:ms" }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.prettyPrint());
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize({ all: true }), winston_1.default.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss:ms" }), winston_1.default.format.printf((info) => `${info.timestamp} ${info.level}: ${info.message}`));
const transports = [];
if (process.env.NODE_ENV === "development") {
    transports.push(new winston_1.default.transports.Console({
        format: consoleFormat,
        level: "debug",
    }));
}
else {
    transports.push(new winston_1.default.transports.Console({
        format: logFormat,
        level: "info",
    }));
}
if (process.env.NODE_ENV === "production") {
    transports.push(new winston_1.default.transports.File({
        filename: path_1.default.join(logsDir, "error.log"),
        level: "error",
        format: logFormat,
        maxsize: 5242880,
        maxFiles: 5,
    }));
    transports.push(new winston_1.default.transports.File({
        filename: path_1.default.join(logsDir, "combined.log"),
        format: logFormat,
        maxsize: 5242880,
        maxFiles: 5,
    }));
    transports.push(new winston_1.default.transports.File({
        filename: path_1.default.join(logsDir, "access.log"),
        level: "http",
        format: logFormat,
        maxsize: 5242880,
        maxFiles: 5,
    }));
}
const logger = winston_1.default.createLogger({
    level: process.env.LOG_LEVEL || (process.env.NODE_ENV === "development" ? "debug" : "info"),
    levels: logLevels,
    format: logFormat,
    transports,
    exitOnError: false,
});
exports.logger = logger;
class Logger {
    static error(message, meta) {
        logger.error(message, meta);
    }
    static warn(message, meta) {
        logger.warn(message, meta);
    }
    static info(message, meta) {
        logger.info(message, meta);
    }
    static http(message, meta) {
        logger.http(message, meta);
    }
    static debug(message, meta) {
        logger.debug(message, meta);
    }
    static auth(message, userId, meta) {
        logger.info(`[AUTH] ${message}`, { userId, ...meta });
    }
    static database(message, query, meta) {
        logger.debug(`[DATABASE] ${message}`, { query, ...meta });
    }
    static api(message, endpoint, method, meta) {
        logger.http(`[API] ${message}`, { endpoint, method, ...meta });
    }
    static security(message, ip, meta) {
        logger.warn(`[SECURITY] ${message}`, { ip, ...meta });
    }
    static performance(message, duration, meta) {
        logger.info(`[PERFORMANCE] ${message}`, { duration, ...meta });
    }
    static queue(message, queueName, jobId, meta) {
        logger.info(`[QUEUE] ${message}`, { queueName, jobId, ...meta });
    }
    static cache(message, key, meta) {
        logger.debug(`[CACHE] ${message}`, { key, ...meta });
    }
    static websocket(message, userId, meta) {
        logger.debug(`[WEBSOCKET] ${message}`, { userId, ...meta });
    }
    static requestLogger() {
        return (req, res, next) => {
            const start = Date.now();
            res.on("finish", () => {
                const duration = Date.now() - start;
                const { method, url, ip } = req;
                const { statusCode } = res;
                const message = `${method} ${url} ${statusCode} - ${duration}ms`;
                if (statusCode >= 400) {
                    Logger.error(message, {
                        method,
                        url,
                        statusCode,
                        duration,
                        ip,
                        userAgent: req.get("User-Agent"),
                        userId: req.user?.userId,
                    });
                }
                else {
                    Logger.http(message, {
                        method,
                        url,
                        statusCode,
                        duration,
                        ip,
                        userId: req.user?.userId,
                    });
                }
            });
            next();
        };
    }
    static errorLogger() {
        return (err, req, res, next) => {
            const { method, url, ip } = req;
            Logger.error(`Unhandled error: ${err.message}`, {
                error: err.stack,
                method,
                url,
                ip,
                userAgent: req.get("User-Agent"),
                userId: req.user?.userId,
                body: req.body,
                params: req.params,
                query: req.query,
            });
            next(err);
        };
    }
    static async measurePerformance(operation, fn, meta) {
        const start = Date.now();
        try {
            const result = await fn();
            const duration = Date.now() - start;
            Logger.performance(`${operation} completed`, duration, meta);
            return result;
        }
        catch (error) {
            const duration = Date.now() - start;
            Logger.error(`${operation} failed after ${duration}ms`, {
                error: error.message,
                duration,
                ...meta,
            });
            throw error;
        }
    }
    static startup(message, meta) {
        logger.info(`[STARTUP] ${message}`, meta);
    }
    static shutdown(message, meta) {
        logger.info(`[SHUTDOWN] ${message}`, meta);
    }
    static health(service, status, meta) {
        const level = status === "healthy" ? "info" : "error";
        logger[level](`[HEALTH] ${service} is ${status}`, meta);
    }
    static business(event, userId, meta) {
        logger.info(`[BUSINESS] ${event}`, { userId, ...meta });
    }
}
exports.Logger = Logger;
exports.morganStream = {
    write: (message) => {
        Logger.http(message.trim());
    },
};
exports.default = Logger;
//# sourceMappingURL=loggerService.js.map