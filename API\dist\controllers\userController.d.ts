import { Request, Response, NextFunction } from "express";
interface UpdateProfileRequest {
    full_name?: string;
    mobile_number?: string;
    current_location?: string;
    linkedin_url?: string;
    company?: string;
    job_title?: string;
    course_id?: number;
    batch_year?: number;
    privacy_settings?: {
        show_email?: boolean;
        show_mobile?: boolean;
        show_linkedin?: boolean;
    };
}
export declare const getProfile: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const updateProfile: (req: Request<{}, {}, UpdateProfileRequest>, res: Response, next: NextFunction) => Promise<void>;
export declare const getUserDirectory: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getUserById: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getConnections: (req: Request, res: Response, next: NextFunction) => Promise<never>;
export declare const sendConnectionRequest: (req: Request, res: Response, next: NextFunction) => Promise<never>;
export declare const respondToConnection: (req: Request, res: Response, next: NextFunction) => Promise<never>;
export declare const getConnectionRequests: (req: Request, res: Response, next: NextFunction) => Promise<never>;
export declare const uploadProfilePicture: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export {};
//# sourceMappingURL=userController.d.ts.map