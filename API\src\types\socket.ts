import { Socket } from "socket.io";

// Extended Socket interface with user information
export interface AuthenticatedSocket extends Socket {
  userId?: number;
  tenantId?: number;
  userRole?: string;
  isAuthenticated?: boolean;
}

// User presence status
export enum UserStatus {
  ONLINE = "online",
  AWAY = "away",
  BUSY = "busy",
  OFFLINE = "offline",
}

// Online user information
export interface OnlineUser {
  userId: number;
  tenantId: number;
  fullName: string;
  status: UserStatus;
  lastSeen: Date;
  socketId: string;
}

// Message interfaces
export interface ChatMessage {
  id?: string;
  senderId: number;
  receiverId?: number;
  roomId?: string;
  content: string;
  messageType: MessageType;
  timestamp: Date;
  isRead?: boolean;
  isDelivered?: boolean;
  attachments?: MessageAttachment[];
}

export enum MessageType {
  TEXT = "text",
  IMAGE = "image",
  FILE = "file",
  SYSTEM = "system",
}

export interface MessageAttachment {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  url: string;
}

// Typing indicator
export interface TypingIndicator {
  userId: number;
  userName: string;
  roomId?: string | undefined;
  receiverId?: number | undefined;
  isTyping: boolean;
}

// Notification interfaces
export interface SocketNotification {
  id?: string;
  userId: number;
  tenantId: number;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  isRead?: boolean;
  createdAt: Date;
}

export enum NotificationType {
  FOLLOW_REQUEST = "follow_request",
  FOLLOW_ACCEPTED = "follow_accepted",
  CONNECTION_REQUEST = "connection_request",
  CONNECTION_ACCEPTED = "connection_accepted",
  MESSAGE = "message",
  EVENT_INVITATION = "event_invitation",
  EVENT_REMINDER = "event_reminder",
  JOB_POSTED = "job_posted",
  POST_LIKED = "post_liked",
  POST_COMMENTED = "post_commented",
  SYSTEM = "system",
}

// Follow/Connection request interfaces
export interface FollowRequest {
  id?: number;
  followerId: number;
  followingId: number;
  tenantId: number;
  status: FollowStatus;
  followerName: string;
  followerAvatar?: string;
  createdAt: Date;
}

export enum FollowStatus {
  PENDING = "PENDING",
  ACCEPTED = "ACCEPTED",
  REJECTED = "REJECTED",
  BLOCKED = "BLOCKED",
}

// Event-related interfaces
export interface EventUpdate {
  eventId: number;
  tenantId: number;
  authorId: number;
  title: string;
  description?: string;
  startTime: Date;
  endTime?: Date;
  location?: string;
  updateType: EventUpdateType;
  updatedBy: string;
}

export enum EventUpdateType {
  CREATED = "created",
  UPDATED = "updated",
  DELETED = "deleted",
  RSVP_ADDED = "rsvp_added",
  RSVP_REMOVED = "rsvp_removed",
}

// Job-related interfaces
export interface JobUpdate {
  jobId: number;
  tenantId: number;
  authorId: number;
  title: string;
  companyName: string;
  updateType: JobUpdateType;
  updatedBy: string;
}

export enum JobUpdateType {
  POSTED = "posted",
  UPDATED = "updated",
  DELETED = "deleted",
  APPLICATION_RECEIVED = "application_received",
}

// Post-related interfaces
export interface PostUpdate {
  postId: number;
  tenantId: number;
  authorId: number;
  title?: string;
  content: string;
  updateType: PostUpdateType;
  updatedBy: string;
  likesCount?: number;
  commentsCount?: number;
}

export enum PostUpdateType {
  CREATED = "created",
  UPDATED = "updated",
  DELETED = "deleted",
  LIKED = "liked",
  UNLIKED = "unliked",
  COMMENTED = "commented",
}

// Socket event data interfaces
export interface SocketEventData {
  [key: string]: any;
}

// Authentication data
export interface SocketAuthData {
  token: string;
  userId?: number;
  tenantId?: number;
}

// Error response
export interface SocketError {
  code: string;
  message: string;
  details?: any;
}

// Room join/leave data
export interface RoomData {
  roomId: string;
  roomType: RoomType;
  metadata?: Record<string, any>;
}

export enum RoomType {
  CHAT = "chat",
  EVENT = "event",
  JOB = "job",
  POST = "post",
  NOTIFICATION = "notification",
  TENANT = "tenant",
}

// Socket middleware data
export interface SocketMiddlewareData {
  userId: number;
  tenantId: number;
  userRole: string;
  permissions: string[];
}

// Real-time activity data
export interface ActivityData {
  userId: number;
  tenantId: number;
  activityType: ActivityType;
  resourceId?: number;
  resourceType?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export enum ActivityType {
  USER_JOINED = "user_joined",
  USER_LEFT = "user_left",
  MESSAGE_SENT = "message_sent",
  POST_CREATED = "post_created",
  EVENT_CREATED = "event_created",
  JOB_POSTED = "job_posted",
  FOLLOW_REQUEST_SENT = "follow_request_sent",
  CONNECTION_MADE = "connection_made",
}
