# WebSocket Implementation Documentation

This document provides comprehensive information about the WebSocket implementation using Socket.IO in the IonAlumni backend.

## Overview

The WebSocket implementation provides real-time communication features including:
- User presence tracking
- Real-time notifications
- Private messaging
- Live updates for events, jobs, and posts
- Follow/connection request handling
- Typing indicators
- Room-based communication

## Architecture

### Core Components

1. **Socket Configuration** (`src/config/socket.ts`)
   - Socket.IO server configuration
   - Event constants
   - Room naming conventions

2. **Socket Service** (`src/services/socketService.ts`)
   - Main service for handling WebSocket operations
   - User connection/disconnection management
   - Notification broadcasting

3. **Socket Handlers** (`src/handlers/socketHandlers.ts`)
   - Event handlers for all WebSocket events
   - Authentication and validation

4. **Socket Middleware** (`src/middleware/socketAuth.ts`)
   - Authentication middleware for WebSocket connections
   - Rate limiting and authorization

5. **Notification Service** (`src/services/notificationService.ts`)
   - Integration with WebSocket for real-time notifications
   - Event-specific notification handling

6. **Socket Utils** (`src/utils/socketUtils.ts`)
   - Utility functions for triggering WebSocket events from controllers
   - Helper methods for common operations

## Authentication

WebSocket connections require JWT authentication:

```javascript
// Client-side connection with authentication
const socket = io('http://localhost:3001', {
  auth: {
    token: 'your-jwt-token-here'
  }
});
```

## Events

### Connection Events
- `connection` - User connects
- `disconnect` - User disconnects
- `authenticate` - Authenticate connection
- `authenticated` - Authentication successful

### User Presence Events
- `user_online` - User comes online
- `user_offline` - User goes offline
- `user_status_change` - User status changes
- `get_online_users` - Request online users list
- `online_users_list` - Receive online users list

### Messaging Events
- `send_message` - Send private message
- `receive_message` - Receive private message
- `message_delivered` - Message delivery confirmation
- `message_read` - Message read receipt
- `typing_start` - Start typing indicator
- `typing_stop` - Stop typing indicator

### Notification Events
- `send_notification` - Send notification
- `receive_notification` - Receive notification
- `notification_read` - Mark notification as read
- `notification_count` - Notification count update

### Follow/Connection Events
- `follow_request` - Send follow request
- `follow_accepted` - Follow request accepted
- `follow_rejected` - Follow request rejected
- `connection_request` - Send connection request
- `connection_accepted` - Connection accepted

### Real-time Update Events
- `event_created` - New event created
- `event_updated` - Event updated
- `event_deleted` - Event deleted
- `job_posted` - New job posted
- `job_updated` - Job updated
- `post_created` - New post created
- `post_liked` - Post liked
- `post_commented` - Post commented

### Room Events
- `join_room` - Join a room
- `leave_room` - Leave a room
- `room_message` - Room message

## Rooms

The system uses various room types for organizing communications:

- `tenant:{tenantId}` - All users in a tenant
- `user:{userId}` - User-specific room
- `notifications:{userId}` - User notifications
- `online:{tenantId}` - Online users in tenant
- `private:{userId1}:{userId2}` - Private chat between users
- `event:{eventId}` - Event-specific room
- `job:{jobId}` - Job-specific room
- `post:{postId}` - Post-specific room

## Usage Examples

### Client-Side Connection

```javascript
import io from 'socket.io-client';

const socket = io('http://localhost:3001', {
  auth: {
    token: localStorage.getItem('authToken')
  }
});

// Listen for authentication
socket.on('authenticated', (data) => {
  console.log('Authenticated:', data);
});

// Listen for notifications
socket.on('receive_notification', (notification) => {
  console.log('New notification:', notification);
  // Update UI with notification
});

// Listen for online users
socket.on('online_users_list', (users) => {
  console.log('Online users:', users);
  // Update online users list in UI
});

// Send a message
socket.emit('send_message', {
  receiverId: 123,
  content: 'Hello!',
  messageType: 'text'
});

// Listen for messages
socket.on('receive_message', (message) => {
  console.log('New message:', message);
  // Update chat UI
});
```

### Server-Side Integration

```javascript
import { SocketUtils } from '../utils/socketUtils';

// In your controller
export const createEvent = async (req, res) => {
  try {
    // Create event in database
    const event = await prisma.event.create({...});
    
    // Trigger real-time notification
    await SocketUtils.triggerEventCreated(
      event.id, 
      event.tenant_id, 
      event.author_id
    );
    
    res.json({ success: true, data: event });
  } catch (error) {
    res.status(500).json({ error: 'Failed to create event' });
  }
};
```

## API Endpoints

The system provides REST endpoints for WebSocket management:

- `GET /api/socket/online-users` - Get online users
- `GET /api/socket/user/:userId/online` - Check if user is online
- `POST /api/socket/test-notification` - Send test notification
- `POST /api/socket/broadcast-notification` - Broadcast to multiple users
- `POST /api/socket/system-notification` - Send system notification
- `GET /api/socket/stats` - Get server statistics
- `POST /api/socket/disconnect-user` - Force disconnect user (admin)
- `GET /api/socket/room/:roomId/members` - Get room members
- `POST /api/socket/activity` - Send activity update

## Error Handling

The system includes comprehensive error handling:

```javascript
// Client-side error handling
socket.on('error', (error) => {
  console.error('Socket error:', error);
  // Handle different error types
  switch (error.code) {
    case 'AUTH_FAILED':
      // Redirect to login
      break;
    case 'RATE_LIMIT_EXCEEDED':
      // Show rate limit message
      break;
    default:
      // Show generic error
  }
});

socket.on('validation_error', (error) => {
  console.error('Validation error:', error);
  // Show validation error to user
});
```

## Rate Limiting

WebSocket events are rate-limited to prevent abuse:
- Default: 100 events per minute per user
- Configurable per event type
- Automatic cleanup of expired rate limit data

## Security Features

1. **JWT Authentication** - All connections must be authenticated
2. **Tenant Isolation** - Users can only interact within their tenant
3. **Rate Limiting** - Prevents spam and abuse
4. **Input Validation** - All event data is validated
5. **Permission Checks** - Role-based access control

## Monitoring and Debugging

### Server Statistics
```javascript
// Get real-time server stats
const stats = SocketUtils.getServerStats();
console.log('Total connections:', stats.totalConnections);
console.log('Online users:', stats.totalOnlineUsers);
```

### Logging
All WebSocket events are logged with appropriate levels:
- Connection/disconnection events
- Authentication attempts
- Error conditions
- Rate limit violations

## Performance Considerations

1. **Connection Pooling** - Efficient socket management
2. **Room Optimization** - Strategic room usage to minimize broadcasts
3. **Memory Management** - Automatic cleanup of disconnected users
4. **Rate Limiting** - Prevents resource exhaustion
5. **Selective Broadcasting** - Only send events to relevant users

## Deployment Notes

1. **Clustering** - Socket.IO supports clustering with Redis adapter
2. **Load Balancing** - Use sticky sessions for WebSocket connections
3. **CORS Configuration** - Properly configure CORS for WebSocket connections
4. **SSL/TLS** - Use secure connections in production

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Check JWT token validity
   - Verify CORS configuration
   - Check network connectivity

2. **Authentication Errors**
   - Ensure token is passed correctly
   - Verify token hasn't expired
   - Check user account status

3. **Missing Notifications**
   - Verify user is online
   - Check room membership
   - Verify notification service integration

### Debug Mode

Enable debug logging by setting environment variable:
```bash
DEBUG=socket.io:* npm run dev
```

## Future Enhancements

1. **Redis Integration** - For horizontal scaling
2. **Message Persistence** - Store messages in database
3. **File Sharing** - Support for file attachments
4. **Video/Audio Calls** - WebRTC integration
5. **Push Notifications** - Mobile push notification integration
