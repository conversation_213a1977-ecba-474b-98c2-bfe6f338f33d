# Image Upload System

This document describes the file system-based image upload implementation that replaces Cloudinary.

## Overview

The image upload system uses the local file system to store uploaded images within the API folder structure. Images are organized into subdirectories and served as static files.

## Directory Structure

```
API/
├── uploads/
│   ├── profiles/     # Profile pictures
│   ├── posts/        # Post images
│   └── temp/         # Temporary uploads
```

## Configuration

### Environment Variables

```env
# File Upload Configuration
MAX_FILE_SIZE=10485760    # 10MB in bytes
UPLOAD_PATH=./uploads/    # Relative to API folder
```

### Supported File Types

- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

## API Endpoints

### Upload Profile Picture

**POST** `/api/users/profile/picture`

- **Authentication**: Required
- **Content-Type**: `multipart/form-data`
- **Field Name**: `profilePicture`
- **Max File Size**: 10MB

**Example Request:**
```bash
curl -X POST \
  http://localhost:5000/api/users/profile/picture \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "profilePicture=@/path/to/image.jpg"
```

**Example Response:**
```json
{
  "message": "Profile picture uploaded successfully",
  "profilePicture": {
    "url": "/uploads/profiles/profilePicture-1642123456789-123456789.jpg",
    "uploadedAt": "2024-01-14T10:30:45.123Z"
  },
  "user": {
    "id": 1,
    "full_name": "John Doe",
    "email": "<EMAIL>"
  },
  "timestamp": "2024-01-14T10:30:45.123Z"
}
```

## File Management

### Automatic Cleanup

- Temporary files older than 24 hours are automatically cleaned up
- Cleanup runs every 6 hours
- Old profile pictures are automatically deleted when new ones are uploaded

### File Naming Convention

Files are renamed using the following pattern:
```
{fieldName}-{timestamp}-{randomNumber}.{extension}
```

Example: `profilePicture-1642123456789-123456789.jpg`

## Security Features

1. **File Type Validation**: Only image files are allowed
2. **File Size Limits**: Configurable maximum file size (default 10MB)
3. **Unique Filenames**: Prevents conflicts and directory traversal
4. **Authentication Required**: All upload endpoints require valid JWT tokens
5. **Error Handling**: Failed uploads are automatically cleaned up

## Static File Serving

Uploaded images are served as static files from the `/uploads` endpoint:

```
http://localhost:5000/uploads/profiles/profilePicture-1642123456789-123456789.jpg
```

## Error Handling

Common error responses:

### File Too Large
```json
{
  "error": "File too large",
  "message": "File size must be less than 10MB"
}
```

### Invalid File Type
```json
{
  "error": "Only image files (JPEG, PNG, GIF, WebP) are allowed"
}
```

### No File Uploaded
```json
{
  "error": "No file uploaded"
}
```

## Implementation Details

### Key Components

1. **Upload Middleware** (`src/middleware/upload.ts`)
   - Multer configuration
   - File validation
   - Storage configuration

2. **File Service** (`src/services/fileService.ts`)
   - File operations
   - Cleanup utilities
   - Path management

3. **Upload Controller** (`src/controllers/userController.ts`)
   - Profile picture upload handler
   - Database updates
   - Error handling

### Database Integration

Profile pictures are stored in the `user_profiles` table:
- Field: `profile_picture_url`
- Type: `VARCHAR`
- Example: `/uploads/profiles/profilePicture-1642123456789-123456789.jpg`

## Migration from Cloudinary

This implementation replaces Cloudinary with a local file system approach:

1. **Removed Dependencies**: `cloudinary` package
2. **Added Dependencies**: `multer`, `@types/multer`
3. **Configuration Changes**: Removed Cloudinary env vars, added upload path config
4. **Storage Location**: Files stored in `API/uploads/` instead of cloud storage

## Future Enhancements

Potential improvements for production use:

1. **Image Optimization**: Add image resizing and compression
2. **CDN Integration**: Serve files through a CDN for better performance
3. **Backup Strategy**: Implement file backup to cloud storage
4. **Monitoring**: Add file storage monitoring and alerts
5. **Batch Operations**: Support for multiple file uploads
