{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AACA,2CAAsD;AACtD,wCAA0C;AAC1C,iDAA4C;AAC5C,iDAA6C;AAmBtC,MAAM,YAAY,GAAG,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACjG,IAAI,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAChF,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAElF,MAAM,KAAK,GAAG,gBAAS,CAAC,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QAEpF,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAA,0BAAW,EAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,OAAO,GAAG,gBAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAGnD,MAAM,IAAI,GAAG,MAAM,iBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvC,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,SAAS,EAAE,IAAI;qBAChB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YACpC,MAAM,IAAA,0BAAW,EAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,mBAAU,CAAC,WAAW,EAAE,CAAC;YACnD,MAAM,IAAA,0BAAW,EAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,mBAAU,CAAC,QAAQ,EAAE,CAAC;YAChD,MAAM,IAAA,0BAAW,EAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC;QAC3D,CAAC;QAGD,GAAG,CAAC,IAAI,GAAG;YACT,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,cAAc;YAC3B,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YACtB,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC;YACjD,CAAC;YACD,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AA7DW,QAAA,YAAY,gBA6DvB;AAKK,MAAM,eAAe,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC9F,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,mBAAU,CAAC,QAAQ,EAAE,CAAC;QAC5C,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAVW,QAAA,eAAe,mBAU1B;AAKK,MAAM,SAAS,GAAG,CAAC,GAAG,KAAiB,EAAE,EAAE;IAChD,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACtE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAgB,CAAC,EAAE,CAAC;YAC/C,OAAO,IAAI,CAAC,IAAA,0BAAW,EAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,SAAS,aAYpB;AAKW,QAAA,kBAAkB,GAAG,IAAA,iBAAS,EAAC,iBAAQ,CAAC,YAAY,EAAE,iBAAQ,CAAC,WAAW,CAAC,CAAC;AAK5E,QAAA,iBAAiB,GAAG,IAAA,iBAAS,EAAC,iBAAQ,CAAC,WAAW,CAAC,CAAC;AAKpD,QAAA,aAAa,GAAG,IAAA,iBAAS,EAAC,iBAAQ,CAAC,OAAO,CAAC,CAAC;AAK5C,QAAA,cAAc,GAAG,IAAA,iBAAS,EAAC,iBAAQ,CAAC,OAAO,CAAC,CAAC;AAK7C,QAAA,oBAAoB,GAAG,IAAA,iBAAS,EAAC,iBAAQ,CAAC,OAAO,EAAE,iBAAQ,CAAC,YAAY,EAAE,iBAAQ,CAAC,WAAW,CAAC,CAAC;AAKhG,QAAA,sBAAsB,GAAG,IAAA,iBAAS,EAAC,iBAAQ,CAAC,OAAO,EAAE,iBAAQ,CAAC,OAAO,CAAC,CAAC;AAK7E,MAAM,YAAY,GAAG,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACjG,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,gBAAS,CAAC,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAE1E,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,OAAO,GAAG,gBAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAEnD,MAAM,IAAI,GAAG,MAAM,iBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACvC,OAAO,EAAE;oBACP,MAAM,EAAE;wBACN,MAAM,EAAE;4BACN,SAAS,EAAE,IAAI;yBAChB;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,mBAAU,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACjF,GAAG,CAAC,IAAI,GAAG;oBACT,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,MAAM,EAAE,IAAI,CAAC,cAAc;oBAC3B,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;oBACtB,SAAS,EAAE,IAAI,CAAC,SAAS;iBAC1B,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;IAEjB,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAlCW,QAAA,YAAY,gBAkCvB"}