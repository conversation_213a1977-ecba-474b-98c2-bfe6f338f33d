{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../src/utils/auth.ts"], "names": [], "mappings": "AAIA,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,SAAS;IACxB,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED,MAAM,WAAW,mBAAmB;IAClC,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,qBAAa,SAAS;IACpB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAoC;IACtE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAA4C;IACtF,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAsC;IAC5E,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,sBAAsB,CAA+C;IAC7F,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAoD;WAe5E,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;WAO/C,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAO9E,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,UAAU,GAAG,MAAM;IAWvD,MAAM,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,GAAG,MAAM;IAWxD,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,GAAG,GAAG,SAAS;IAiB9C,MAAM,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,GAAG,UAAU;IAUnD,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,GAAG,UAAU;IAUpD,MAAM,CAAC,sBAAsB,CAAC,UAAU,EAAE,MAAM,GAAG,SAAS,GAAG,MAAM,GAAG,IAAI;IAU5E,MAAM,CAAC,sBAAsB,CAAC,MAAM,GAAE,MAAW,GAAG,MAAM;IAY1D,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG;QAAE,OAAO,EAAE,OAAO,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAE;IAgCjF,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO;IAQ5C,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;IASxC,MAAM,CAAC,yBAAyB,CAAC,KAAK,EAAE,MAAM,GAAG,mBAAmB,GAAG,IAAI;IAW3E,MAAM,CAAC,0BAA0B,CAAC,OAAO,EAAE,UAAU,GAAG,MAAM;IAW9D,MAAM,CAAC,2BAA2B,CAAC,OAAO,EAAE,mBAAmB,GAAG,MAAM;CAOzE"}