import { Socket } from "socket.io";
import { ExtendedError } from "socket.io/dist/namespace";
import { AuthenticatedSocket, SocketError } from "../types/socket";
export declare const socketAuthMiddleware: (socket: AuthenticatedSocket, next: (err?: ExtendedError) => void) => Promise<void>;
export declare const requireRole: (allowedRoles: string[]) => (socket: AuthenticatedSocket, next: (err?: ExtendedError) => void) => void;
export declare const requireSameTenant: (targetTenantId: number) => (socket: AuthenticatedSocket, next: (err?: ExtendedError) => void) => void;
export declare const validateEventData: <T>(validator: (data: any) => data is T) => (data: any, socket: AuthenticatedSocket, next: (err?: ExtendedError) => void) => void;
export declare const socketRateLimit: (maxEvents?: number, windowMs?: number) => (socket: AuthenticatedSocket, next: (err?: ExtendedError) => void) => void;
export declare const emitSocketError: (socket: Socket, error: SocketError) => void;
export declare const emitValidationError: (socket: Socket, message: string, details?: any) => void;
export declare const socketEventLogger: (eventName: string) => (socket: AuthenticatedSocket, data: any, next: (err?: ExtendedError) => void) => void;
//# sourceMappingURL=socketAuth.d.ts.map