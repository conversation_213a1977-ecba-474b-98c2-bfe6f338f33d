import { Socket } from "socket.io";
export interface AuthenticatedSocket extends Socket {
    userId?: number;
    tenantId?: number;
    userRole?: string;
    isAuthenticated?: boolean;
}
export declare enum UserStatus {
    ONLINE = "online",
    AWAY = "away",
    BUSY = "busy",
    OFFLINE = "offline"
}
export interface OnlineUser {
    userId: number;
    tenantId: number;
    fullName: string;
    status: UserStatus;
    lastSeen: Date;
    socketId: string;
}
export interface ChatMessage {
    id?: string;
    senderId: number;
    receiverId?: number;
    roomId?: string;
    content: string;
    messageType: MessageType;
    timestamp: Date;
    isRead?: boolean;
    isDelivered?: boolean;
    attachments?: MessageAttachment[];
}
export declare enum MessageType {
    TEXT = "text",
    IMAGE = "image",
    FILE = "file",
    SYSTEM = "system"
}
export interface MessageAttachment {
    id: string;
    fileName: string;
    fileSize: number;
    fileType: string;
    url: string;
}
export interface TypingIndicator {
    userId: number;
    userName: string;
    roomId?: string | undefined;
    receiverId?: number | undefined;
    isTyping: boolean;
}
export interface SocketNotification {
    id?: string;
    userId: number;
    tenantId: number;
    type: NotificationType;
    title: string;
    message: string;
    data?: Record<string, any>;
    isRead?: boolean;
    createdAt: Date;
}
export declare enum NotificationType {
    FOLLOW_REQUEST = "follow_request",
    FOLLOW_ACCEPTED = "follow_accepted",
    CONNECTION_REQUEST = "connection_request",
    CONNECTION_ACCEPTED = "connection_accepted",
    MESSAGE = "message",
    EVENT_INVITATION = "event_invitation",
    EVENT_REMINDER = "event_reminder",
    JOB_POSTED = "job_posted",
    POST_LIKED = "post_liked",
    POST_COMMENTED = "post_commented",
    SYSTEM = "system"
}
export interface FollowRequest {
    id?: number;
    followerId: number;
    followingId: number;
    tenantId: number;
    status: FollowStatus;
    followerName: string;
    followerAvatar?: string;
    createdAt: Date;
}
export declare enum FollowStatus {
    PENDING = "PENDING",
    ACCEPTED = "ACCEPTED",
    REJECTED = "REJECTED",
    BLOCKED = "BLOCKED"
}
export interface EventUpdate {
    eventId: number;
    tenantId: number;
    authorId: number;
    title: string;
    description?: string;
    startTime: Date;
    endTime?: Date;
    location?: string;
    updateType: EventUpdateType;
    updatedBy: string;
}
export declare enum EventUpdateType {
    CREATED = "created",
    UPDATED = "updated",
    DELETED = "deleted",
    RSVP_ADDED = "rsvp_added",
    RSVP_REMOVED = "rsvp_removed"
}
export interface JobUpdate {
    jobId: number;
    tenantId: number;
    authorId: number;
    title: string;
    companyName: string;
    updateType: JobUpdateType;
    updatedBy: string;
}
export declare enum JobUpdateType {
    POSTED = "posted",
    UPDATED = "updated",
    DELETED = "deleted",
    APPLICATION_RECEIVED = "application_received"
}
export interface PostUpdate {
    postId: number;
    tenantId: number;
    authorId: number;
    title?: string;
    content: string;
    updateType: PostUpdateType;
    updatedBy: string;
    likesCount?: number;
    commentsCount?: number;
}
export declare enum PostUpdateType {
    CREATED = "created",
    UPDATED = "updated",
    DELETED = "deleted",
    LIKED = "liked",
    UNLIKED = "unliked",
    COMMENTED = "commented"
}
export interface SocketEventData {
    [key: string]: any;
}
export interface SocketAuthData {
    token: string;
    userId?: number;
    tenantId?: number;
}
export interface SocketError {
    code: string;
    message: string;
    details?: any;
}
export interface RoomData {
    roomId: string;
    roomType: RoomType;
    metadata?: Record<string, any>;
}
export declare enum RoomType {
    CHAT = "chat",
    EVENT = "event",
    JOB = "job",
    POST = "post",
    NOTIFICATION = "notification",
    TENANT = "tenant"
}
export interface SocketMiddlewareData {
    userId: number;
    tenantId: number;
    userRole: string;
    permissions: string[];
}
export interface ActivityData {
    userId: number;
    tenantId: number;
    activityType: ActivityType;
    resourceId?: number;
    resourceType?: string;
    metadata?: Record<string, any>;
    timestamp: Date;
}
export declare enum ActivityType {
    USER_JOINED = "user_joined",
    USER_LEFT = "user_left",
    MESSAGE_SENT = "message_sent",
    POST_CREATED = "post_created",
    EVENT_CREATED = "event_created",
    JOB_POSTED = "job_posted",
    FOLLOW_REQUEST_SENT = "follow_request_sent",
    CONNECTION_MADE = "connection_made"
}
//# sourceMappingURL=socket.d.ts.map