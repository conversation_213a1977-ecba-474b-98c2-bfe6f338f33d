{"version": 3, "file": "responseOptimization.js", "sourceRoot": "", "sources": ["../../src/middleware/responseOptimization.ts"], "names": [], "mappings": ";;;AACA,6DAAmD;AAGnD,MAAM,mBAAmB,GAAG;IAE1B,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,OAAO;IAG/C,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,OAAO;IAGpE,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,OAAO;IAGnE,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,UAAU,CAAC;IAGtE,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,KAAK,OAAO;CACjE,CAAC;AAGK,MAAM,cAAc,GAAG,GAAG,EAAE;IACjC,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,CAAC;YAC9C,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAgB,CAAC;QAC1C,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,OAAiB,CAAC;QAE5C,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAGD,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAGxC,GAAG,CAAC,IAAI,GAAG,UAAU,IAAS;YAC5B,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBAChE,OAAO,YAAY,CAAC,YAAY,CAAC,CAAC;YACpC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,sBAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;oBACrC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC7D,MAAM;oBACN,OAAO;oBACP,IAAI,EAAE,GAAG,CAAC,IAAI;iBACf,CAAC,CAAC;gBACH,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAlCW,QAAA,cAAc,kBAkCzB;AAGF,SAAS,mBAAmB,CAAC,IAAS,EAAE,MAAe,EAAE,OAAgB;IACvE,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;IACxE,CAAC;IAED,IAAI,MAAM,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;IAGzB,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAC7D,MAAM,cAAc,GAAQ,EAAE,CAAC;QAE/B,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;YAClC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAExB,MAAM,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC7C,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC5B,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;oBAC9B,CAAC;oBACD,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACrC,cAAc,CAAC,MAAM,CAAC,GAAG;wBACvB,GAAG,cAAc,CAAC,MAAM,CAAC;wBACzB,GAAG,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,WAAW,CAAC;qBACpD,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxC,cAAc,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;QAED,MAAM,GAAG,cAAc,CAAC;IAC1B,CAAC;IAGD,IAAI,OAAO,EAAE,CAAC;QACZ,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAE9D,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;YAClC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAExB,MAAM,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC7C,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACrC,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;gBAC/E,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;YACvB,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAGM,MAAM,mBAAmB,GAAG,GAAG,EAAE;IACtC,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAEzD,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAExC,GAAG,CAAC,IAAI,GAAG,UAAU,IAAS;YAC5B,IAAI,CAAC;gBACH,IAAI,aAAa,GAAG,IAAI,CAAC;gBAGzB,IAAI,mBAAmB,CAAC,UAAU,EAAE,CAAC;oBAEnC,aAAa,GAAG,iBAAiB,CAAC,aAAa,CAAC,CAAC;gBACnD,CAAC;gBAGD,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC;gBAC1D,IAAI,YAAY,GAAG,mBAAmB,CAAC,eAAe,EAAE,CAAC;oBACvD,sBAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;wBACrC,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI;wBACnD,IAAI,EAAE,GAAG,CAAC,IAAI;wBACd,MAAM,EAAE,GAAG,CAAC,MAAM;qBACnB,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO,YAAY,CAAC,aAAa,CAAC,CAAC;YACrC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,sBAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;oBAC1C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC7D,IAAI,EAAE,GAAG,CAAC,IAAI;iBACf,CAAC,CAAC;gBACH,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AArCW,QAAA,mBAAmB,uBAqC9B;AAGF,SAAS,iBAAiB,CAAC,GAAQ;IACjC,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QACtC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QACjG,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;IACpD,CAAC;IAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,MAAM,OAAO,GAAQ,EAAE,CAAC;QACxB,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/C,MAAM,YAAY,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9C,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;gBAC/B,OAAO,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;gBAC5B,SAAS,GAAG,IAAI,CAAC;YACnB,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;IACzC,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAGM,MAAM,sBAAsB,GAAG,GAAG,EAAE;IACzC,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,CAAC;YAC9C,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAmB,CAAC;QAEhD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAGD,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAExC,GAAG,CAAC,IAAI,GAAG,UAAU,IAAS;YAC5B,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,mBAAmB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC7D,OAAO,YAAY,CAAC,eAAe,CAAC,CAAC;YACvC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,sBAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;oBAC7C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC7D,SAAS;oBACT,IAAI,EAAE,GAAG,CAAC,IAAI;iBACf,CAAC,CAAC;gBACH,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AA/BW,QAAA,sBAAsB,0BA+BjC;AAGF,SAAS,mBAAmB,CAAC,IAAS,EAAE,SAAiB;IACvD,QAAQ,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;QAChC,KAAK,SAAS;YACZ,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;QAE7B,KAAK,WAAW;YACd,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAEtC,KAAK,WAAW;YACd,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAEtC,KAAK,SAAS;YACZ,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAEjC;YACE,sBAAM,CAAC,IAAI,CAAC,2BAA2B,SAAS,EAAE,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC;AAGD,SAAS,aAAa,CAAC,GAAQ,EAAE,MAAM,GAAG,EAAE;IAC1C,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1D,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,SAAS,GAAQ,EAAE,CAAC;IAE1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/C,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QAEjD,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAChE,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAGD,SAAS,sBAAsB,CAAC,GAAQ;IACtC,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QACpC,OAAO,GAAG,CAAC;IACb,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,SAAS,GAAQ,EAAE,CAAC;IAC1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/C,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;QAC/E,SAAS,CAAC,QAAQ,CAAC,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAGD,SAAS,sBAAsB,CAAC,GAAQ;IACtC,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QACpC,OAAO,GAAG,CAAC;IACb,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,SAAS,GAAQ,EAAE,CAAC;IAC1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/C,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC/E,SAAS,CAAC,QAAQ,CAAC,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAGM,MAAM,YAAY,GAAG,CAC1B,UAKI,EAAE,EACN,EAAE;IACF,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;YAC5C,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,EACJ,MAAM,GAAG,GAAG,EACZ,OAAO,EAAE,SAAS,GAAG,KAAK,EAC1B,OAAO,GAAG,KAAK,EACf,cAAc,GAAG,KAAK,GACvB,GAAG,OAAO,CAAC;QAGZ,MAAM,cAAc,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5C,GAAG,CAAC,MAAM,GAAG,UAAU,IAAY;YACjC,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;gBAChB,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,qCAAqC,CAAC,CAAC;gBAChE,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;gBAC9B,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YAC1B,CAAC;YACD,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC;QAGF,IAAI,GAAG,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YACzB,IAAI,YAAY,GAAG,EAAE,CAAC;YAEtB,IAAI,OAAO,EAAE,CAAC;gBACZ,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,IAAI,SAAS,EAAE,CAAC;oBACd,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC/B,CAAC;qBAAM,CAAC;oBACN,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC9B,CAAC;gBACD,YAAY,CAAC,IAAI,CAAC,WAAW,MAAM,EAAE,CAAC,CAAC;YACzC,CAAC;YAED,IAAI,cAAc,EAAE,CAAC;gBACnB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACvC,CAAC;YAED,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAGlD,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxC,GAAG,CAAC,IAAI,GAAG,UAAU,IAAS;gBAC5B,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;gBAChC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAGtB,IAAI,GAAG,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE,CAAC;oBACtC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;gBAC/B,CAAC;gBAED,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC,CAAC;QACJ,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AArEW,QAAA,YAAY,gBAqEvB;AAGF,SAAS,YAAY,CAAC,IAAS;IAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;IACjC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACrC,OAAO,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;AACvE,CAAC;AAGM,MAAM,sBAAsB,GAAG,GAAG,EAAE;IACzC,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,IAAc,CAAC,IAAI,CAAC,CAAC;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,KAAe,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;QACvE,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAGlC,GAAG,CAAC,UAAU,GAAG;YACf,IAAI;YACJ,KAAK;YACL,MAAM;SACP,CAAC;QAGF,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAExC,GAAG,CAAC,IAAI,GAAG,UAAU,IAAS;YAE5B,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC/E,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;gBAEjD,MAAM,iBAAiB,GAAG;oBACxB,IAAI,EAAE,IAAI,CAAC,KAAK;oBAChB,UAAU,EAAE;wBACV,IAAI;wBACJ,KAAK;wBACL,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,UAAU;wBACV,OAAO,EAAE,IAAI,GAAG,UAAU;wBAC1B,OAAO,EAAE,IAAI,GAAG,CAAC;wBACjB,QAAQ,EAAE,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;wBAC7C,QAAQ,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;wBACpC,OAAO,EAAE,SAA+B;wBACxC,OAAO,EAAE,SAA+B;qBACzC;iBACF,CAAC;gBAGF,MAAM,OAAO,GAAG,GAAG,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;gBAClE,MAAM,WAAW,GAAG,IAAI,eAAe,CAAC,GAAG,CAAC,KAAY,CAAC,CAAC;gBAE1D,IAAI,IAAI,GAAG,UAAU,EAAE,CAAC;oBACtB,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC/C,iBAAiB,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,OAAO,IAAI,WAAW,EAAE,CAAC;gBACrE,CAAC;gBAED,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;oBACb,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC/C,iBAAiB,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,OAAO,IAAI,WAAW,EAAE,CAAC;gBACrE,CAAC;gBAED,OAAO,YAAY,CAAC,iBAAiB,CAAC,CAAC;YACzC,CAAC;YAED,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AA3DW,QAAA,sBAAsB,0BA2DjC"}