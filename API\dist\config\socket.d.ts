import { Server as SocketIOServer, ServerOptions } from "socket.io";
import { Server as HTTPServer } from "http";
export declare const socketConfig: Partial<ServerOptions>;
export declare const initializeSocket: (server: HTTPServer) => SocketIOServer;
export declare const getSocketIO: () => SocketIOServer;
export declare const SOCKET_EVENTS: {
    readonly CONNECTION: "connection";
    readonly DISCONNECT: "disconnect";
    readonly CONNECT_ERROR: "connect_error";
    readonly AUTHENTICATE: "authenticate";
    readonly AUTHENTICATED: "authenticated";
    readonly AUTHENTICATION_ERROR: "authentication_error";
    readonly USER_ONLINE: "user_online";
    readonly USER_OFFLINE: "user_offline";
    readonly USER_STATUS_CHANGE: "user_status_change";
    readonly GET_ONLINE_USERS: "get_online_users";
    readonly ONLINE_USERS_LIST: "online_users_list";
    readonly SEND_MESSAGE: "send_message";
    readonly RECEIVE_MESSAGE: "receive_message";
    readonly MESSAGE_DELIVERED: "message_delivered";
    readonly MESSAGE_READ: "message_read";
    readonly TYPING_START: "typing_start";
    readonly TYPING_STOP: "typing_stop";
    readonly TYPING_INDICATOR: "typing_indicator";
    readonly SEND_NOTIFICATION: "send_notification";
    readonly RECEIVE_NOTIFICATION: "receive_notification";
    readonly NOTIFICATION_READ: "notification_read";
    readonly NOTIFICATION_COUNT: "notification_count";
    readonly FOLLOW_REQUEST: "follow_request";
    readonly FOLLOW_ACCEPTED: "follow_accepted";
    readonly FOLLOW_REJECTED: "follow_rejected";
    readonly CONNECTION_REQUEST: "connection_request";
    readonly CONNECTION_ACCEPTED: "connection_accepted";
    readonly CONNECTION_REJECTED: "connection_rejected";
    readonly EVENT_CREATED: "event_created";
    readonly EVENT_UPDATED: "event_updated";
    readonly EVENT_DELETED: "event_deleted";
    readonly EVENT_RSVP: "event_rsvp";
    readonly JOB_POSTED: "job_posted";
    readonly JOB_UPDATED: "job_updated";
    readonly JOB_DELETED: "job_deleted";
    readonly JOB_APPLICATION: "job_application";
    readonly POST_CREATED: "post_created";
    readonly POST_UPDATED: "post_updated";
    readonly POST_DELETED: "post_deleted";
    readonly POST_LIKED: "post_liked";
    readonly POST_COMMENTED: "post_commented";
    readonly JOIN_ROOM: "join_room";
    readonly LEAVE_ROOM: "leave_room";
    readonly ROOM_MESSAGE: "room_message";
    readonly ERROR: "error";
    readonly VALIDATION_ERROR: "validation_error";
};
export declare const SOCKET_ROOMS: {
    readonly TENANT: (tenantId: number) => string;
    readonly USER: (userId: number) => string;
    readonly CHAT: (chatId: string) => string;
    readonly PRIVATE_CHAT: (userId1: number, userId2: number) => string;
    readonly EVENT: (eventId: number) => string;
    readonly JOB: (jobId: number) => string;
    readonly POST: (postId: number) => string;
    readonly NOTIFICATIONS: (userId: number) => string;
    readonly ONLINE_USERS: (tenantId: number) => string;
};
export type SocketEvent = typeof SOCKET_EVENTS[keyof typeof SOCKET_EVENTS];
export type SocketRoom = string;
//# sourceMappingURL=socket.d.ts.map