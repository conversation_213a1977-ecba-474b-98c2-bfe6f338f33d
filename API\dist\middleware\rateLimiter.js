"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRateLimiter = exports.rateLimiter = void 0;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const cache_1 = require("../config/cache");
const loggerService_1 = require("../services/loggerService");
class NodeCacheStore {
    constructor() {
        this.cache = cache_1.rateLimitCache;
    }
    async increment(key) {
        const current = this.cache.get(key) || 0;
        const totalHits = current + 1;
        if (current === 0) {
            const windowMs = parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000");
            this.cache.set(key, totalHits, Math.ceil(windowMs / 1000));
            const resetTime = new Date(Date.now() + windowMs);
            return { totalHits, timeToExpire: windowMs, resetTime };
        }
        else {
            this.cache.set(key, totalHits);
            const ttl = this.cache.getTtl(key);
            const timeToExpire = ttl ? Math.max(0, ttl - Date.now()) : 0;
            const resetTime = ttl ? new Date(ttl) : new Date(Date.now() + 900000);
            return { totalHits, timeToExpire, resetTime };
        }
    }
    async decrement(key) {
        const current = this.cache.get(key) || 0;
        if (current > 0) {
            this.cache.set(key, current - 1);
        }
    }
    async resetKey(key) {
        this.cache.del(key);
    }
}
const memoryStore = new NodeCacheStore();
exports.rateLimiter = (0, express_rate_limit_1.default)({
    store: memoryStore,
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"),
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "100"),
    message: {
        error: "Too many requests from this IP, please try again later.",
        retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000") / 1000),
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        loggerService_1.Logger.security("Rate limit exceeded", req.ip, {
            userAgent: req.get("User-Agent"),
            endpoint: req.path,
            method: req.method,
        });
        res.status(429).json({
            error: "Too many requests from this IP, please try again later.",
            retryAfter: Math.ceil(parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000") / 1000),
        });
    },
    skip: (req) => {
        return req.path === "/health";
    },
});
exports.authRateLimiter = (0, express_rate_limit_1.default)({
    store: new NodeCacheStore(),
    windowMs: 15 * 60 * 1000,
    max: 5,
    message: {
        error: "Too many authentication attempts, please try again later.",
        retryAfter: 900,
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (_req, res) => {
        res.status(429).json({
            error: "Too many authentication attempts, please try again later.",
            retryAfter: 900,
        });
    },
});
loggerService_1.Logger.info("Rate limiters initialized with optimized memory store", {
    generalWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"),
    generalMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "100"),
    authWindowMs: 15 * 60 * 1000,
    authMaxRequests: 5,
});
//# sourceMappingURL=rateLimiter.js.map