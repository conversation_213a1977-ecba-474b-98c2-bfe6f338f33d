{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/utils/auth.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAoC;AACpC,iDAAmC;AAsBnC,MAAa,SAAS;IAoBpB,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAgB;QACxC,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACnD,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,IAAY;QACzD,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;IAKD,MAAM,CAAC,mBAAmB,CAAC,OAAmB;QAC5C,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YACxC,SAAS,EAAE,IAAI,CAAC,cAAc;YAC9B,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,qBAAqB;SACb,CAAC,CAAC;IACxB,CAAC;IAKD,MAAM,CAAC,oBAAoB,CAAC,OAAmB;QAC7C,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAChD,SAAS,EAAE,IAAI,CAAC,sBAAsB;YACtC,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,qBAAqB;SACb,CAAC,CAAC;IACxB,CAAC;IAKD,MAAM,CAAC,iBAAiB,CAAC,IAAS;QAChC,MAAM,OAAO,GAAe;YAC1B,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC1B,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM;SAC3C,CAAC;QAEF,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YAC9C,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;SACjD,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,iBAAiB,CAAC,KAAa;QACpC,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE;YACxC,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,qBAAqB;SAChC,CAAe,CAAC;IACnB,CAAC;IAKD,MAAM,CAAC,kBAAkB,CAAC,KAAa;QACrC,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAChD,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,qBAAqB;SAChC,CAAe,CAAC;IACnB,CAAC;IAKD,MAAM,CAAC,sBAAsB,CAAC,UAA8B;QAC1D,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAKD,MAAM,CAAC,sBAAsB,CAAC,SAAiB,EAAE;QAC/C,MAAM,OAAO,GAAG,wEAAwE,CAAC;QACzF,IAAI,QAAQ,GAAG,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,QAAQ,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QACzE,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKD,MAAM,CAAC,gBAAgB,CAAC,QAAgB;QACtC,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,uCAAuC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5D,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QACtE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,aAAa,CAAC,KAAa;QAChC,MAAM,UAAU,GAAG,4BAA4B,CAAC;QAChD,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAKD,MAAM,CAAC,WAAW,CAAC,GAAW;QAE5B,MAAM,QAAQ,GAAG,wBAAwB,CAAC;QAC1C,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAKD,MAAM,CAAC,yBAAyB,CAAC,KAAa;QAC5C,IAAI,CAAC;YACH,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAwB,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,0BAA0B,CAAC,OAAmB;QACnD,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YACxC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,KAAK;YACvD,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,qBAAqB;SACb,CAAC,CAAC;IACxB,CAAC;IAKD,MAAM,CAAC,2BAA2B,CAAC,OAA4B;QAC7D,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE;YAChD,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI,IAAI;YACvD,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,qBAAqB;SACb,CAAC,CAAC;IACxB,CAAC;;AAhMH,8BAiMC;AAhMyB,oBAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAoB,CAAC;AAC9C,4BAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,kBAA4B,CAAC;AAC9D,wBAAc,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,IAAI,CAAC;AACpD,gCAAsB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,KAAK,CAAC;AACrE,uBAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,IAAI,CAAC,CAAC;AAEzF;IAEE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;QAC5B,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;IACjE,CAAC;IACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC;QACpC,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;IACzE,CAAC;AACH,CAAC,GAAA,CAAA"}