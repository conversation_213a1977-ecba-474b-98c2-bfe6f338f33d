import { Router } from "express";
import { authenticate, requireApproved } from "../middleware/auth";
import {
  updateProfileValidation,
  paginationValidation,
  idValidation,
  connectionRequestValidation,
  connectionResponseValidation,
} from "../middleware/validation";
import { uploadProfilePicture, handleUploadError } from "../middleware/upload";
import * as userController from "../controllers/userController";

const router = Router();

// All routes require authentication
router.use(authenticate);

// Get user profile (own profile)
router.get("/profile", userController.getProfile);

// Update user profile (own profile)
router.put("/profile", updateProfileValidation, userController.updateProfile);

// Upload profile picture
router.post("/profile/picture", uploadProfilePicture, handleUploadError, userController.uploadProfilePicture);

// Get user directory (approved users only)
router.get("/directory", requireApproved, paginationValidation, userController.getUserDirectory);

// Get specific user by ID
router.get("/:id", requireApproved, idValidation, userController.getUserById);

// Get user connections
router.get("/connections/list", requireApproved, paginationValidation, userController.getConnections);

// Send connection request
router.post("/connections/request", requireApproved, connectionRequestValidation, userController.sendConnectionRequest);

// Respond to connection request
router.put(
  "/connections/:id/respond",
  requireApproved,
  idValidation,
  connectionResponseValidation,
  userController.respondToConnection
);

// Get connection requests (received)
router.get("/connections/requests", requireApproved, paginationValidation, userController.getConnectionRequests);

export default router;
