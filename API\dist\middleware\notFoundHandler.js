"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFoundHandler = void 0;
const notFoundHandler = (req, res) => {
    res.status(404).json({
        error: "Route not found",
        message: `The requested endpoint ${req.method} ${req.path} does not exist`,
        timestamp: new Date().toISOString(),
        availableEndpoints: {
            auth: "/api/auth",
            users: "/api/users",
            health: "/health",
        },
    });
};
exports.notFoundHandler = notFoundHandler;
//# sourceMappingURL=notFoundHandler.js.map