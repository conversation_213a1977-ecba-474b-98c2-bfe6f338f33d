import { Server as SocketIOServer } from "socket.io";
import { AuthenticatedSocket, OnlineUser, UserStatus, ChatMessage, SocketNotification, FollowRequest, EventUpdate, JobUpdate, PostUpdate, TypingIndicator } from "../types/socket";
declare class SocketService {
    private io;
    private onlineUsers;
    private userSockets;
    private typingUsers;
    initialize(io: SocketIOServer): void;
    private getIO;
    handleUserConnection(socket: AuthenticatedSocket): Promise<void>;
    handleUserDisconnection(socket: AuthenticatedSocket): Promise<void>;
    sendNotificationToUser(userId: number, notification: SocketNotification): Promise<void>;
    sendNotificationToUsers(userIds: number[], notification: SocketNotification): Promise<void>;
    sendNotificationToTenant(tenantId: number, notification: SocketNotification): Promise<void>;
    sendPrivateMessage(senderId: number, receiverId: number, message: ChatMessage): Promise<void>;
    handleTypingIndicator(indicator: TypingIndicator): void;
    handleFollowRequest(followRequest: FollowRequest): Promise<void>;
    handleEventUpdate(eventUpdate: EventUpdate): Promise<void>;
    handleJobUpdate(jobUpdate: JobUpdate): Promise<void>;
    handlePostUpdate(postUpdate: PostUpdate): Promise<void>;
    getOnlineUsersForTenant(tenantId: number): OnlineUser[];
    isUserOnline(userId: number): boolean;
    getUserSocketIds(userId: number): string[];
    private getUnreadNotificationCount;
    updateUserStatus(userId: number, status: UserStatus): Promise<void>;
    joinRoom(socket: AuthenticatedSocket, roomId: string, roomType: string): Promise<void>;
    leaveRoom(socket: AuthenticatedSocket, roomId: string, roomType: string): Promise<void>;
    broadcastToRoom(roomId: string, event: string, data: any, excludeUserId?: number): Promise<void>;
    getRoomMembers(roomId: string): Promise<number[]>;
    cleanup(): void;
}
export declare const socketService: SocketService;
export default socketService;
//# sourceMappingURL=socketService.d.ts.map