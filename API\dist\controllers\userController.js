"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadProfilePicture = exports.getConnectionRequests = exports.respondToConnection = exports.sendConnectionRequest = exports.getConnections = exports.getUserById = exports.getUserDirectory = exports.updateProfile = exports.getProfile = void 0;
const client_1 = require("@prisma/client");
const database_1 = require("../config/database");
const errorHandler_1 = require("../middleware/errorHandler");
const fileService_1 = require("../services/fileService");
const getProfile = async (req, res, next) => {
    try {
        if (!req.user) {
            throw (0, errorHandler_1.createError)("User not authenticated", 401);
        }
        const user = await database_1.prisma.user.findUnique({
            where: { id: parseInt(req.user.userId) },
            include: {
                tenant: {
                    select: {
                        id: true,
                        name: true,
                        subdomain: true,
                    },
                },
                profile: {
                    include: {
                        course: {
                            select: {
                                id: true,
                                course_name: true,
                            },
                        },
                    },
                },
                _count: {
                    select: {
                        general_posts: true,
                        jobs: true,
                    },
                },
            },
        });
        if (!user) {
            throw (0, errorHandler_1.createError)("User not found", 404);
        }
        res.json({
            user,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getProfile = getProfile;
const updateProfile = async (req, res, next) => {
    try {
        if (!req.user) {
            throw (0, errorHandler_1.createError)("User not authenticated", 401);
        }
        const { full_name, mobile_number, current_location, linkedin_url, company, job_title, course_id, batch_year, privacy_settings, } = req.body;
        const userUpdateData = {};
        if (full_name !== undefined)
            userUpdateData.full_name = full_name;
        if (mobile_number !== undefined)
            userUpdateData.mobile_number = mobile_number;
        let updatedUser;
        if (Object.keys(userUpdateData).length > 0) {
            updatedUser = await database_1.prisma.user.update({
                where: { id: parseInt(req.user.userId) },
                data: userUpdateData,
            });
        }
        const profileUpdateData = {};
        if (current_location !== undefined)
            profileUpdateData.current_location = current_location;
        if (linkedin_url !== undefined)
            profileUpdateData.linkedin_url = linkedin_url;
        if (company !== undefined)
            profileUpdateData.company = company;
        if (job_title !== undefined)
            profileUpdateData.job_title = job_title;
        if (course_id !== undefined)
            profileUpdateData.course_id = course_id;
        if (batch_year !== undefined)
            profileUpdateData.batch_year = batch_year;
        if (privacy_settings !== undefined) {
            const existingProfile = await database_1.prisma.userProfile.findUnique({
                where: { user_id: parseInt(req.user.userId) },
                select: { privacy_settings: true },
            });
            profileUpdateData.privacy_settings = {
                ...(existingProfile?.privacy_settings || {}),
                ...privacy_settings,
            };
        }
        if (Object.keys(profileUpdateData).length > 0) {
            await database_1.prisma.userProfile.upsert({
                where: { user_id: parseInt(req.user.userId) },
                update: profileUpdateData,
                create: {
                    user_id: parseInt(req.user.userId),
                    tenant_id: req.user.tenant_id,
                    ...profileUpdateData,
                },
            });
        }
        const user = await database_1.prisma.user.findUnique({
            where: { id: parseInt(req.user.userId) },
            include: {
                profile: {
                    include: {
                        course: {
                            select: {
                                id: true,
                                course_name: true,
                            },
                        },
                    },
                },
            },
        });
        res.json({
            message: "Profile updated successfully",
            user,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.updateProfile = updateProfile;
const getUserDirectory = async (req, res, next) => {
    try {
        if (!req.user) {
            throw (0, errorHandler_1.createError)("User not authenticated", 401);
        }
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const search = req.query.search;
        const role = req.query.role;
        const course = req.query.course;
        const batch_year = req.query.batch_year;
        const company = req.query.company;
        const skip = (page - 1) * limit;
        const currentUser = await database_1.prisma.user.findUnique({
            where: { id: parseInt(req.user.userId) },
            select: { tenant_id: true },
        });
        if (!currentUser) {
            throw (0, errorHandler_1.createError)("User not found", 404);
        }
        const where = {
            tenant_id: currentUser.tenant_id,
            account_status: client_1.UserStatus.APPROVED,
            NOT: {
                id: parseInt(req.user.userId),
            },
        };
        if (search) {
            where.OR = [
                { full_name: { contains: search, mode: "insensitive" } },
                { profile: { company: { contains: search, mode: "insensitive" } } },
                { profile: { job_title: { contains: search, mode: "insensitive" } } },
                { profile: { current_location: { contains: search, mode: "insensitive" } } },
            ];
        }
        if (role) {
            where.role = role;
        }
        if (course) {
            where.profile = {
                ...where.profile,
                course: {
                    course_name: { contains: course, mode: "insensitive" },
                },
            };
        }
        if (batch_year) {
            where.profile = {
                ...where.profile,
                batch_year: parseInt(batch_year),
            };
        }
        if (company) {
            where.profile = {
                ...where.profile,
                company: { contains: company, mode: "insensitive" },
            };
        }
        const [users, total] = await Promise.all([
            database_1.prisma.user.findMany({
                where,
                include: {
                    profile: {
                        include: {
                            course: {
                                select: {
                                    course_name: true,
                                },
                            },
                        },
                    },
                },
                skip,
                take: limit,
                orderBy: [
                    { role: "asc" },
                    { full_name: "asc" },
                ],
            }),
            database_1.prisma.user.count({ where }),
        ]);
        const filteredUsers = users.map((user) => {
            const privacySettings = user.profile?.privacy_settings || {};
            return {
                ...user,
                email: privacySettings.show_email ? user.email : null,
                mobile_number: privacySettings.show_mobile ? user.mobile_number : null,
                profile: user.profile
                    ? {
                        ...user.profile,
                        linkedin_url: privacySettings.show_linkedin ? user.profile.linkedin_url : null,
                    }
                    : null,
            };
        });
        res.json({
            users: filteredUsers,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit),
                hasNext: page < Math.ceil(total / limit),
                hasPrev: page > 1,
            },
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUserDirectory = getUserDirectory;
const getUserById = async (req, res, next) => {
    try {
        const { id } = req.params;
        if (!id) {
            throw (0, errorHandler_1.createError)("User ID is required", 400);
        }
        if (!req.user) {
            throw (0, errorHandler_1.createError)("User not authenticated", 401);
        }
        const currentUser = await database_1.prisma.user.findUnique({
            where: { id: parseInt(req.user.userId) },
            select: { tenant_id: true },
        });
        if (!currentUser) {
            throw (0, errorHandler_1.createError)("Current user not found", 404);
        }
        const user = await database_1.prisma.user.findFirst({
            where: {
                id: parseInt(id),
                tenant_id: currentUser.tenant_id,
                account_status: client_1.UserStatus.APPROVED,
            },
            include: {
                profile: {
                    include: {
                        course: {
                            select: {
                                course_name: true,
                            },
                        },
                    },
                },
                _count: {
                    select: {
                        general_posts: true,
                        jobs: true,
                    },
                },
            },
        });
        if (!user) {
            throw (0, errorHandler_1.createError)("User not found", 404);
        }
        const privacySettings = user.profile?.privacy_settings || {};
        const filteredUser = {
            ...user,
            email: privacySettings.show_email ? user.email : null,
            mobile_number: privacySettings.show_mobile ? user.mobile_number : null,
            profile: user.profile
                ? {
                    ...user.profile,
                    linkedin_url: privacySettings.show_linkedin ? user.profile.linkedin_url : null,
                }
                : null,
        };
        res.json({
            user: filteredUser,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUserById = getUserById;
const getConnections = async (req, res, next) => {
    throw (0, errorHandler_1.createError)("Connections feature not available", 501);
};
exports.getConnections = getConnections;
const sendConnectionRequest = async (req, res, next) => {
    throw (0, errorHandler_1.createError)("Connections feature not available", 501);
};
exports.sendConnectionRequest = sendConnectionRequest;
const respondToConnection = async (req, res, next) => {
    throw (0, errorHandler_1.createError)("Connections feature not available", 501);
};
exports.respondToConnection = respondToConnection;
const getConnectionRequests = async (req, res, next) => {
    throw (0, errorHandler_1.createError)("Connections feature not available", 501);
};
exports.getConnectionRequests = getConnectionRequests;
const uploadProfilePicture = async (req, res, next) => {
    try {
        if (!req.user) {
            throw (0, errorHandler_1.createError)("User not authenticated", 401);
        }
        if (!req.file) {
            throw (0, errorHandler_1.createError)("No file uploaded", 400);
        }
        const validation = fileService_1.FileService.validateImageFile(req.file);
        if (!validation.isValid) {
            fileService_1.FileService.deleteFile(fileService_1.FileService.getRelativePath(req.file.path));
            throw (0, errorHandler_1.createError)(validation.error || "Invalid file", 400);
        }
        const relativePath = fileService_1.FileService.getRelativePath(req.file.path);
        const imageUrl = fileService_1.FileService.getFileUrl(relativePath);
        const currentProfile = await database_1.prisma.userProfile.findUnique({
            where: { user_id: parseInt(req.user.userId) },
            select: { profile_picture_url: true },
        });
        const updatedProfile = await database_1.prisma.userProfile.upsert({
            where: { user_id: parseInt(req.user.userId) },
            update: {
                profile_picture_url: imageUrl,
                updated_at: new Date(),
            },
            create: {
                user_id: parseInt(req.user.userId),
                tenant_id: req.user.tenant_id,
                profile_picture_url: imageUrl,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        full_name: true,
                        email: true,
                    },
                },
            },
        });
        if (currentProfile?.profile_picture_url && currentProfile.profile_picture_url !== imageUrl) {
            const oldRelativePath = currentProfile.profile_picture_url.replace("/uploads/", "");
            fileService_1.FileService.deleteFile(oldRelativePath);
        }
        res.json({
            message: "Profile picture uploaded successfully",
            profilePicture: {
                url: imageUrl,
                uploadedAt: new Date().toISOString(),
            },
            user: updatedProfile.user,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        if (req.file) {
            fileService_1.FileService.deleteFile(fileService_1.FileService.getRelativePath(req.file.path));
        }
        next(error);
    }
};
exports.uploadProfilePicture = uploadProfilePicture;
//# sourceMappingURL=userController.js.map