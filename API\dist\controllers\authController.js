"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetPassword = exports.forgotPassword = exports.verifyEmail = exports.getCurrentUser = exports.refreshToken = exports.logout = exports.login = exports.register = void 0;
const client_1 = require("@prisma/client");
const database_1 = require("../config/database");
const auth_1 = require("../utils/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const register = async (req, res, next) => {
    try {
        const { email, password, full_name, mobile_number, usn, course_name, batch_year, role, tenant_id } = req.body;
        const isAdmin = role === client_1.UserRole.TENANT_ADMIN || role === client_1.UserRole.SUPER_ADMIN;
        if (!isAdmin && !usn) {
            throw (0, errorHandler_1.createError)("USN is required for students and alumni", 400);
        }
        if (!isAdmin && !course_name) {
            throw (0, errorHandler_1.createError)("Course name is required for students and alumni", 400);
        }
        const tenant = await database_1.prisma.tenant.findFirst({
            where: {
                id: tenant_id,
                is_active: true,
            },
        });
        if (!tenant) {
            throw (0, errorHandler_1.createError)("Invalid or inactive tenant", 400);
        }
        const whereClause = {
            tenant_id,
            OR: [{ email }],
        };
        if (usn) {
            whereClause.OR.push({ usn });
        }
        const existingUser = await database_1.prisma.user.findFirst({
            where: whereClause,
        });
        if (existingUser) {
            if (existingUser.email === email) {
                throw (0, errorHandler_1.createError)("User with this email already exists in this organization", 409);
            }
            if (usn && existingUser.usn === usn) {
                throw (0, errorHandler_1.createError)("User with this USN already exists in this organization", 409);
            }
        }
        const hashedPassword = await auth_1.AuthUtils.hashPassword(password);
        let course = null;
        if (!isAdmin && course_name) {
            course = await database_1.prisma.course.findFirst({
                where: {
                    tenant_id,
                    course_name,
                },
            });
            if (!course) {
                course = await database_1.prisma.course.create({
                    data: {
                        tenant_id,
                        course_name,
                    },
                });
            }
        }
        const user = await database_1.prisma.user.create({
            data: {
                tenant_id,
                email,
                password_hash: hashedPassword,
                full_name,
                mobile_number: mobile_number ?? null,
                usn: usn || `ADMIN_${Date.now()}`,
                role,
                account_status: isAdmin ? client_1.UserStatus.APPROVED : client_1.UserStatus.PENDING,
            },
            select: {
                id: true,
                tenant_id: true,
                email: true,
                full_name: true,
                usn: true,
                role: true,
                account_status: true,
                created_at: true,
            },
        });
        if (!isAdmin || course) {
            await database_1.prisma.userProfile.create({
                data: {
                    user_id: user.id,
                    tenant_id,
                    course_id: course?.id || null,
                    batch_year: batch_year || null,
                    privacy_settings: {
                        show_email: false,
                        show_mobile: false,
                        show_linkedin: true,
                    },
                },
            });
        }
        res.status(201).json({
            message: isAdmin
                ? "Admin registration successful. Your account is approved."
                : "Registration successful. Your account is pending approval.",
            user: {
                ...user,
                course_name: course?.course_name || null,
                batch_year: batch_year || null,
            },
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.register = register;
const login = async (req, res, next) => {
    try {
        const { email, password } = req.body;
        const user = await database_1.prisma.user.findFirst({
            where: {
                email,
                tenant: {
                    is_active: true,
                },
            },
            include: {
                tenant: {
                    select: {
                        id: true,
                        name: true,
                        subdomain: true,
                        is_active: true,
                    },
                },
            },
        });
        if (!user) {
            throw (0, errorHandler_1.createError)("Invalid email or password", 401);
        }
        const isPasswordValid = await auth_1.AuthUtils.comparePassword(password, user.password_hash);
        if (!isPasswordValid) {
            throw (0, errorHandler_1.createError)("Invalid email or password", 401);
        }
        if (user.account_status === client_1.UserStatus.REJECTED) {
            throw (0, errorHandler_1.createError)("Your account has been rejected. Please contact admin.", 403);
        }
        if (user.account_status === client_1.UserStatus.DEACTIVATED) {
            throw (0, errorHandler_1.createError)("Your account has been deactivated. Please contact admin.", 403);
        }
        if (user.account_status === client_1.UserStatus.PENDING) {
            throw (0, errorHandler_1.createError)("Your account is pending approval. Please wait for admin approval.", 403);
        }
        const tokens = auth_1.AuthUtils.generateTokenPair({
            id: user.id.toString(),
            email: user.email,
            role: user.role,
            account_status: user.account_status,
            tenant_id: user.tenant_id,
        });
        res.json({
            message: "Login successful",
            accessToken: tokens.accessToken,
            user: {
                id: user.id,
                email: user.email,
                full_name: user.full_name,
                role: user.role,
                account_status: user.account_status,
                tenant: user.tenant,
            },
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.login = login;
const logout = async (req, res, next) => {
    try {
        res.clearCookie("refreshToken");
        res.json({
            message: "Logout successful",
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.logout = logout;
const refreshToken = async (req, res, next) => {
    try {
        const { refreshToken } = req.body;
        const token = refreshToken || req.cookies.refreshToken;
        if (!token) {
            throw (0, errorHandler_1.createError)("Refresh token is required", 401);
        }
        const payload = auth_1.AuthUtils.verifyRefreshToken(token);
        const user = await database_1.prisma.user.findUnique({
            where: { id: parseInt(payload.userId) },
            include: {
                tenant: {
                    select: {
                        is_active: true,
                    },
                },
            },
        });
        if (!user || !user.tenant.is_active) {
            throw (0, errorHandler_1.createError)("User not found or tenant inactive", 401);
        }
        if (user.account_status !== client_1.UserStatus.APPROVED) {
            throw (0, errorHandler_1.createError)("Account is not approved", 403);
        }
        const tokens = auth_1.AuthUtils.generateTokenPair({
            id: user.id.toString(),
            email: user.email,
            role: user.role,
            account_status: user.account_status,
            tenant_id: user.tenant_id,
        });
        res.cookie("refreshToken", tokens.refreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: "strict",
            maxAge: 30 * 24 * 60 * 60 * 1000,
        });
        res.json({
            message: "Token refreshed successfully",
            accessToken: tokens.accessToken,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.refreshToken = refreshToken;
const getCurrentUser = async (req, res, next) => {
    try {
        if (!req.user) {
            throw (0, errorHandler_1.createError)("User not authenticated", 401);
        }
        const user = await database_1.prisma.user.findUnique({
            where: { id: parseInt(req.user.userId) },
            include: {
                tenant: {
                    select: {
                        id: true,
                        name: true,
                        subdomain: true,
                    },
                },
                profile: {
                    include: {
                        course: {
                            select: {
                                course_name: true,
                            },
                        },
                    },
                },
            },
        });
        if (!user) {
            throw (0, errorHandler_1.createError)("User not found", 404);
        }
        res.json({
            user,
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCurrentUser = getCurrentUser;
const verifyEmail = async (req, res, next) => {
    try {
        res.status(501).json({
            message: "Email verification feature coming soon",
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.verifyEmail = verifyEmail;
const forgotPassword = async (req, res, next) => {
    try {
        res.status(501).json({
            message: "Forgot password feature coming soon",
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.forgotPassword = forgotPassword;
const resetPassword = async (req, res, next) => {
    try {
        res.status(501).json({
            message: "Reset password feature coming soon",
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        next(error);
    }
};
exports.resetPassword = resetPassword;
//# sourceMappingURL=authController.js.map