"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheKeys = exports.CacheService = exports.checkCacheHealth = exports.rateLimitCache = exports.sessionCache = exports.cache = void 0;
const node_cache_1 = __importDefault(require("node-cache"));
const getCacheConfig = () => {
    const isProduction = process.env.NODE_ENV === "production";
    return {
        stdTTL: parseInt(process.env.CACHE_DEFAULT_TTL || "3600"),
        checkperiod: parseInt(process.env.CACHE_CHECK_PERIOD || "600"),
        useClones: false,
        deleteOnExpire: true,
        enableLegacyCallbacks: false,
        maxKeys: parseInt(process.env.CACHE_MAX_KEYS || "10000"),
        ...(isProduction && {
            checkperiod: 300,
            maxKeys: 50000,
        }),
    };
};
exports.cache = new node_cache_1.default(getCacheConfig());
exports.sessionCache = new node_cache_1.default({
    ...getCacheConfig(),
    stdTTL: parseInt(process.env.SESSION_TTL || "1800"),
    maxKeys: parseInt(process.env.SESSION_MAX_KEYS || "5000"),
});
exports.rateLimitCache = new node_cache_1.default({
    ...getCacheConfig(),
    stdTTL: parseInt(process.env.RATE_LIMIT_TTL || "900"),
    checkperiod: 60,
    maxKeys: parseInt(process.env.RATE_LIMIT_MAX_KEYS || "10000"),
});
const checkCacheHealth = () => {
    try {
        const stats = exports.cache.getStats();
        return stats !== null;
    }
    catch (error) {
        console.error("Cache health check failed:", error);
        return false;
    }
};
exports.checkCacheHealth = checkCacheHealth;
class CacheService {
    static get(key) {
        try {
            const cached = exports.cache.get(key);
            return cached !== undefined ? cached : null;
        }
        catch (error) {
            console.error(`Cache get error for key ${key}:`, error);
            return null;
        }
    }
    static async getAsync(key) {
        return this.get(key);
    }
    static set(key, data, ttl = this.DEFAULT_TTL) {
        try {
            return exports.cache.set(key, data, ttl);
        }
        catch (error) {
            console.error(`Cache set error for key ${key}:`, error);
            return false;
        }
    }
    static async setAsync(key, data, ttl = this.DEFAULT_TTL) {
        return this.set(key, data, ttl);
    }
    static del(key) {
        try {
            return exports.cache.del(key) > 0;
        }
        catch (error) {
            console.error(`Cache delete error for key ${key}:`, error);
            return false;
        }
    }
    static async delAsync(key) {
        return this.del(key);
    }
    static delPattern(pattern) {
        try {
            const keys = exports.cache.keys().filter((key) => key.includes(pattern));
            if (keys.length > 0) {
                return exports.cache.del(keys);
            }
            return 0;
        }
        catch (error) {
            console.error(`Cache delete pattern error for ${pattern}:`, error);
            return 0;
        }
    }
    static async delPatternAsync(pattern) {
        return this.delPattern(pattern);
    }
    static exists(key) {
        try {
            return exports.cache.has(key);
        }
        catch (error) {
            console.error(`Cache exists error for key ${key}:`, error);
            return false;
        }
    }
    static async existsAsync(key) {
        return this.exists(key);
    }
    static incr(key, ttl) {
        try {
            const current = exports.cache.get(key) || 0;
            const result = current + 1;
            exports.cache.set(key, result, ttl || this.DEFAULT_TTL);
            return result;
        }
        catch (error) {
            console.error(`Cache increment error for key ${key}:`, error);
            return 0;
        }
    }
    static async incrAsync(key, ttl) {
        return this.incr(key, ttl);
    }
    static async getOrSet(key, fetchFunction, ttl = this.DEFAULT_TTL) {
        try {
            const cached = this.get(key);
            if (cached !== null) {
                return cached;
            }
            const data = await fetchFunction();
            if (data !== null && data !== undefined) {
                this.set(key, data, ttl);
            }
            return data;
        }
        catch (error) {
            console.error(`Cache getOrSet error for key ${key}:`, error);
            try {
                return await fetchFunction();
            }
            catch (fetchError) {
                console.error(`Fetch function error for key ${key}:`, fetchError);
                return null;
            }
        }
    }
    static mget(keys) {
        const results = new Map();
        try {
            keys.forEach((key) => {
                const value = exports.cache.get(key);
                results.set(key, value !== undefined ? value : null);
            });
        }
        catch (error) {
            console.error("Cache mget error:", error);
            keys.forEach((key) => results.set(key, null));
        }
        return results;
    }
    static async mgetAsync(keys) {
        return this.mget(keys);
    }
    static mset(entries) {
        try {
            let allSuccess = true;
            entries.forEach(({ key, data, ttl = this.DEFAULT_TTL }) => {
                const success = exports.cache.set(key, data, ttl);
                if (!success)
                    allSuccess = false;
            });
            return allSuccess;
        }
        catch (error) {
            console.error("Cache mset error:", error);
            return false;
        }
    }
    static async msetAsync(entries) {
        return this.mset(entries);
    }
    static deletePattern(pattern) {
        try {
            const keys = exports.cache.keys().filter((key) => key.includes(pattern));
            if (keys.length === 0) {
                return 0;
            }
            return exports.cache.del(keys);
        }
        catch (error) {
            console.error(`Cache deletePattern error for pattern ${pattern}:`, error);
            return 0;
        }
    }
    static async deletePatternAsync(pattern) {
        return this.deletePattern(pattern);
    }
    static clear() {
        try {
            exports.cache.flushAll();
            return true;
        }
        catch (error) {
            console.error("Cache clear error:", error);
            return false;
        }
    }
    static async clearAsync() {
        return this.clear();
    }
    static getSize() {
        try {
            return exports.cache.getStats().keys;
        }
        catch (error) {
            console.error("Cache getSize error:", error);
            return 0;
        }
    }
    static async getSizeAsync() {
        return this.getSize();
    }
    static getStats() {
        try {
            return exports.cache.getStats();
        }
        catch (error) {
            console.error("Cache getStats error:", error);
            return null;
        }
    }
    static async getStatsAsync() {
        return this.getStats();
    }
    static getKeys() {
        try {
            return exports.cache.keys();
        }
        catch (error) {
            console.error("Cache getKeys error:", error);
            return [];
        }
    }
}
exports.CacheService = CacheService;
CacheService.DEFAULT_TTL = 3600;
exports.CacheKeys = {
    user: (userId) => `user:${userId}`,
    userProfile: (userId) => `user:profile:${userId}`,
    userConnections: (userId) => `user:connections:${userId}`,
    job: (jobId) => `job:${jobId}`,
    jobsList: (page, filters) => `jobs:list:${page}:${filters}`,
    event: (eventId) => `event:${eventId}`,
    eventsList: (page, filters) => `events:list:${page}:${filters}`,
    post: (postId) => `post:${postId}`,
    postsList: (page, filters) => `posts:list:${page}:${filters}`,
    notifications: (userId) => `notifications:${userId}`,
    dashboardMetrics: () => "dashboard:metrics",
    userSearch: (query, page) => `search:users:${query}:${page}`,
    rateLimitUser: (userId) => `ratelimit:user:${userId}`,
    rateLimitIP: (ip) => `ratelimit:ip:${ip}`,
    session: (sessionId) => `session:${sessionId}`,
};
exports.cache.on("set", (key) => {
    if (process.env.NODE_ENV === "development") {
        console.log(`🔄 Cache SET: ${key}`);
    }
});
exports.cache.on("del", (key) => {
    if (process.env.NODE_ENV === "development") {
        console.log(`🗑️ Cache DEL: ${key}`);
    }
});
exports.cache.on("expired", (key) => {
    if (process.env.NODE_ENV === "development") {
        console.log(`⏰ Cache EXPIRED: ${key}`);
    }
});
exports.cache.on("flush", () => {
    console.log("🧹 Cache flushed");
});
console.log("✅ Node-cache initialized successfully");
console.log(`📊 Cache configuration: TTL=${getCacheConfig().stdTTL}s, MaxKeys=${getCacheConfig().maxKeys}`);
const gracefulCacheShutdown = (signal) => {
    console.log(`Received ${signal}, clearing caches...`);
    try {
        exports.cache.flushAll();
        exports.sessionCache.flushAll();
        exports.rateLimitCache.flushAll();
        console.log("Caches cleared successfully");
    }
    catch (error) {
        console.error("Error during cache shutdown:", error);
    }
};
process.on("SIGTERM", () => gracefulCacheShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulCacheShutdown("SIGINT"));
exports.default = exports.cache;
//# sourceMappingURL=cache.js.map