const { PrismaClient } = require('@prisma/client');
const { AuthUtils } = require('./dist/utils/auth');

const prisma = new PrismaClient();

async function createProperAdmin() {
  try {
    console.log('Creating properly hashed admin user...\n');
    
    const password = 'AdminPassword123!';
    const hashedPassword = await AuthUtils.hashPassword(password);
    
    console.log('Password:', password);
    console.log('Hash:', hashedPassword);
    
    const adminUser = await prisma.user.upsert({
      where: { 
        idx_tenant_email: {
          tenant_id: 1,
          email: '<EMAIL>'
        }
      },
      update: {
        password_hash: hashedPassword,
        account_status: 'APPROVED'
      },
      create: {
        tenant_id: 1,
        full_name: 'Test Admin',
        email: '<EMAIL>',
        password_hash: hashedPassword,
        usn: 'ADMIN001',
        role: 'TENANT_ADMIN',
        account_status: 'APPROVED'
      }
    });
    
    console.log('\n✅ Admin user created/updated:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: AdminPassword123!');
    console.log('   Role:', adminUser.role);
    console.log('   Status:', adminUser.account_status);
    console.log('   ID:', adminUser.id);

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createProperAdmin();
