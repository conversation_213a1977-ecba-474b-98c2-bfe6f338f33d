"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileService = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const config_1 = require("../config/config");
const loggerService_1 = require("./loggerService");
class FileService {
    static getFilePath(relativePath) {
        return path_1.default.join(this.uploadDir, relativePath);
    }
    static getFileUrl(relativePath) {
        return `/uploads/${relativePath.replace(/\\/g, "/")}`;
    }
    static async deleteFile(relativePath) {
        try {
            const fullPath = this.getFilePath(relativePath);
            if (fs_1.default.existsSync(fullPath)) {
                fs_1.default.unlinkSync(fullPath);
                loggerService_1.Logger.info("File deleted successfully", { path: relativePath });
                return true;
            }
            loggerService_1.Logger.warn("File not found for deletion", { path: relativePath });
            return false;
        }
        catch (error) {
            loggerService_1.Logger.error("Error deleting file", {
                path: relativePath,
                error: error instanceof Error ? error.message : String(error),
            });
            return false;
        }
    }
    static fileExists(relativePath) {
        const fullPath = this.getFilePath(relativePath);
        return fs_1.default.existsSync(fullPath);
    }
    static getFileStats(relativePath) {
        try {
            const fullPath = this.getFilePath(relativePath);
            return fs_1.default.statSync(fullPath);
        }
        catch (error) {
            loggerService_1.Logger.error("Error getting file stats", {
                path: relativePath,
                error: error instanceof Error ? error.message : String(error),
            });
            return null;
        }
    }
    static async moveFile(tempPath, permanentPath) {
        try {
            const sourcePath = this.getFilePath(tempPath);
            const destPath = this.getFilePath(permanentPath);
            const destDir = path_1.default.dirname(destPath);
            if (!fs_1.default.existsSync(destDir)) {
                fs_1.default.mkdirSync(destDir, { recursive: true });
            }
            fs_1.default.renameSync(sourcePath, destPath);
            loggerService_1.Logger.info("File moved successfully", { from: tempPath, to: permanentPath });
            return true;
        }
        catch (error) {
            loggerService_1.Logger.error("Error moving file", {
                from: tempPath,
                to: permanentPath,
                error: error instanceof Error ? error.message : String(error),
            });
            return false;
        }
    }
    static async cleanupTempFiles(maxAgeHours = 24) {
        try {
            const tempDir = path_1.default.join(this.uploadDir, "temp");
            if (!fs_1.default.existsSync(tempDir)) {
                return;
            }
            const files = fs_1.default.readdirSync(tempDir);
            const maxAge = maxAgeHours * 60 * 60 * 1000;
            const now = Date.now();
            for (const file of files) {
                const filePath = path_1.default.join(tempDir, file);
                const stats = fs_1.default.statSync(filePath);
                if (now - stats.mtime.getTime() > maxAge) {
                    fs_1.default.unlinkSync(filePath);
                    loggerService_1.Logger.info("Cleaned up old temp file", { file });
                }
            }
        }
        catch (error) {
            loggerService_1.Logger.error("Error cleaning up temp files", {
                error: error instanceof Error ? error.message : String(error),
            });
        }
    }
    static getRelativePath(absolutePath) {
        return path_1.default.relative(this.uploadDir, absolutePath);
    }
    static validateImageFile(file) {
        if (file.size > config_1.config.upload.maxFileSize) {
            return {
                isValid: false,
                error: `File size exceeds ${config_1.config.upload.maxFileSize / (1024 * 1024)}MB limit`,
            };
        }
        const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"];
        if (!allowedTypes.includes(file.mimetype)) {
            return {
                isValid: false,
                error: "Only image files (JPEG, PNG, GIF, WebP) are allowed",
            };
        }
        return { isValid: true };
    }
    static generateUniqueFilename(originalName, prefix = "") {
        const timestamp = Date.now();
        const random = Math.round(Math.random() * 1e9);
        const extension = path_1.default.extname(originalName).toLowerCase();
        return `${prefix}${timestamp}-${random}${extension}`;
    }
    static initializeCleanup() {
        setInterval(() => {
            this.cleanupTempFiles(24);
        }, 6 * 60 * 60 * 1000);
        this.cleanupTempFiles(24);
    }
}
exports.FileService = FileService;
FileService.uploadDir = path_1.default.resolve(__dirname, "../../", config_1.config.upload.uploadPath);
//# sourceMappingURL=fileService.js.map