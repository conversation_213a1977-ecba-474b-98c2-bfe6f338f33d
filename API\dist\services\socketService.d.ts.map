{"version": 3, "file": "socketService.d.ts", "sourceRoot": "", "sources": ["../../src/services/socketService.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,IAAI,cAAc,EAAE,MAAM,WAAW,CAAC;AAGrD,OAAO,EACL,mBAAmB,EACnB,UAAU,EACV,UAAU,EACV,WAAW,EACX,kBAAkB,EAClB,aAAa,EACb,WAAW,EACX,SAAS,EACT,UAAU,EACV,eAAe,EAChB,MAAM,iBAAiB,CAAC;AAKzB,cAAM,aAAa;IACjB,OAAO,CAAC,EAAE,CAA+B;IACzC,OAAO,CAAC,WAAW,CAAiC;IACpD,OAAO,CAAC,WAAW,CAAkC;IACrD,OAAO,CAAC,WAAW,CAAkC;IAGrD,UAAU,CAAC,EAAE,EAAE,cAAc;IAM7B,OAAO,CAAC,KAAK;IAQP,oBAAoB,CAAC,MAAM,EAAE,mBAAmB;IA8DhD,uBAAuB,CAAC,MAAM,EAAE,mBAAmB;IA+CnD,sBAAsB,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,kBAAkB;IAkBvE,uBAAuB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,YAAY,EAAE,kBAAkB;IAM3E,wBAAwB,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,kBAAkB;IAW3E,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW;IAyBnF,qBAAqB,CAAC,SAAS,EAAE,eAAe;IAiB1C,mBAAmB,CAAC,aAAa,EAAE,aAAa;IAwBhD,iBAAiB,CAAC,WAAW,EAAE,WAAW;IAiB1C,eAAe,CAAC,SAAS,EAAE,SAAS;IAiBpC,gBAAgB,CAAC,UAAU,EAAE,UAAU;IAiB7C,uBAAuB,CAAC,QAAQ,EAAE,MAAM,GAAG,UAAU,EAAE;IAKvD,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO;IAKrC,gBAAgB,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE;YAM5B,0BAA0B;IAYlC,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IAuBnD,QAAQ,CAAC,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM;IAoBtE,SAAS,CAAC,MAAM,EAAE,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM;IAoBvE,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,aAAa,CAAC,EAAE,MAAM;IAyBhF,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAqBvD,OAAO;CAMR;AAGD,eAAO,MAAM,aAAa,eAAsB,CAAC;AACjD,eAAe,aAAa,CAAC"}