import { PrismaClient } from "@prisma/client";
import { socketService } from "./socketService";
import { Logger } from "./loggerService";
import {
  SocketNotification,
  NotificationType,
  EventUpdate,
  EventUpdateType,
  JobUpdate,
  JobUpdateType,
  PostUpdate,
  PostUpdateType,
} from "../types/socket";

const prisma = new PrismaClient();

class NotificationService {
  // Send follow request notification
  async sendFollowRequestNotification(followerId: number, followingId: number, tenantId: number) {
    try {
      const follower = await prisma.user.findUnique({
        where: { id: followerId },
        select: { full_name: true },
      });

      if (!follower) return;

      const notification: SocketNotification = {
        userId: followingId,
        tenantId,
        type: NotificationType.FOLLOW_REQUEST,
        title: "New Follow Request",
        message: `${follower.full_name} wants to follow you`,
        data: { followerId, followerName: follower.full_name },
        createdAt: new Date(),
      };

      await socketService.sendNotificationToUser(followingId, notification);
      Logger.info(`Follow request notification sent from ${followerId} to ${followingId}`);
    } catch (error) {
      Logger.error("Error sending follow request notification:", error);
    }
  }

  // Send follow accepted notification
  async sendFollowAcceptedNotification(followerId: number, followingId: number, tenantId: number) {
    try {
      const following = await prisma.user.findUnique({
        where: { id: followingId },
        select: { full_name: true },
      });

      if (!following) return;

      const notification: SocketNotification = {
        userId: followerId,
        tenantId,
        type: NotificationType.FOLLOW_ACCEPTED,
        title: "Follow Request Accepted",
        message: `${following.full_name} accepted your follow request`,
        data: { followingId, followingName: following.full_name },
        createdAt: new Date(),
      };

      await socketService.sendNotificationToUser(followerId, notification);
      Logger.info(`Follow accepted notification sent to ${followerId} from ${followingId}`);
    } catch (error) {
      Logger.error("Error sending follow accepted notification:", error);
    }
  }

  // Send connection request notification
  async sendConnectionRequestNotification(requesterId: number, targetId: number, tenantId: number) {
    try {
      const requester = await prisma.user.findUnique({
        where: { id: requesterId },
        select: { full_name: true },
      });

      if (!requester) return;

      const notification: SocketNotification = {
        userId: targetId,
        tenantId,
        type: NotificationType.CONNECTION_REQUEST,
        title: "New Connection Request",
        message: `${requester.full_name} wants to connect with you`,
        data: { requesterId, requesterName: requester.full_name },
        createdAt: new Date(),
      };

      await socketService.sendNotificationToUser(targetId, notification);
      Logger.info(`Connection request notification sent from ${requesterId} to ${targetId}`);
    } catch (error) {
      Logger.error("Error sending connection request notification:", error);
    }
  }

  // Send connection accepted notification
  async sendConnectionAcceptedNotification(requesterId: number, accepterId: number, tenantId: number) {
    try {
      const accepter = await prisma.user.findUnique({
        where: { id: accepterId },
        select: { full_name: true },
      });

      if (!accepter) return;

      const notification: SocketNotification = {
        userId: requesterId,
        tenantId,
        type: NotificationType.CONNECTION_ACCEPTED,
        title: "Connection Request Accepted",
        message: `${accepter.full_name} accepted your connection request`,
        data: { accepterId, accepterName: accepter.full_name },
        createdAt: new Date(),
      };

      await socketService.sendNotificationToUser(requesterId, notification);
      Logger.info(`Connection accepted notification sent to ${requesterId} from ${accepterId}`);
    } catch (error) {
      Logger.error("Error sending connection accepted notification:", error);
    }
  }

  // Send new message notification
  async sendMessageNotification(senderId: number, receiverId: number, tenantId: number, messageContent: string) {
    try {
      const sender = await prisma.user.findUnique({
        where: { id: senderId },
        select: { full_name: true },
      });

      if (!sender) return;

      const notification: SocketNotification = {
        userId: receiverId,
        tenantId,
        type: NotificationType.MESSAGE,
        title: "New Message",
        message: `${sender.full_name}: ${messageContent.substring(0, 50)}${messageContent.length > 50 ? "..." : ""}`,
        data: { senderId, senderName: sender.full_name, messageContent },
        createdAt: new Date(),
      };

      await socketService.sendNotificationToUser(receiverId, notification);
      Logger.info(`Message notification sent from ${senderId} to ${receiverId}`);
    } catch (error) {
      Logger.error("Error sending message notification:", error);
    }
  }

  // Send event-related notifications
  async sendEventNotification(eventId: number, tenantId: number, type: EventUpdateType, authorId: number) {
    try {
      const event = await prisma.event.findUnique({
        where: { id: eventId },
        include: { author: { select: { full_name: true } } },
      });

      if (!event) return;

      let title = "";
      let message = "";

      switch (type) {
        case EventUpdateType.CREATED:
          title = "New Event Created";
          message = `${event.author.full_name} created a new event: ${event.title}`;
          break;
        case EventUpdateType.UPDATED:
          title = "Event Updated";
          message = `${event.author.full_name} updated the event: ${event.title}`;
          break;
        case EventUpdateType.DELETED:
          title = "Event Cancelled";
          message = `${event.author.full_name} cancelled the event: ${event.title}`;
          break;
        default:
          return;
      }

      const notification: SocketNotification = {
        userId: 0, // Will be set for each user
        tenantId,
        type: NotificationType.EVENT_INVITATION,
        title,
        message,
        data: { eventId, eventTitle: event.title, authorName: event.author.full_name },
        createdAt: new Date(),
      };

      // Send to all users in the tenant (except the author)
      const tenantUsers = await prisma.user.findMany({
        where: {
          tenant_id: tenantId,
          account_status: "APPROVED",
          id: { not: authorId },
        },
        select: { id: true },
      });

      const userIds = tenantUsers.map(user => user.id);
      await socketService.sendNotificationToUsers(userIds, notification);

      // Also send real-time event update
      const eventUpdate: EventUpdate = {
        eventId,
        tenantId,
        authorId,
        title: event.title,
        description: event.description,
        startTime: event.start_time,
        endTime: event.end_time,
        location: event.location,
        updateType: type,
        updatedBy: event.author.full_name,
      };

      await socketService.handleEventUpdate(eventUpdate);
      Logger.info(`Event notification sent for event ${eventId} (${type})`);
    } catch (error) {
      Logger.error("Error sending event notification:", error);
    }
  }

  // Send job-related notifications
  async sendJobNotification(jobId: number, tenantId: number, type: JobUpdateType, authorId: number) {
    try {
      const job = await prisma.job.findUnique({
        where: { id: jobId },
        include: { author: { select: { full_name: true } } },
      });

      if (!job) return;

      let title = "";
      let message = "";

      switch (type) {
        case JobUpdateType.POSTED:
          title = "New Job Posted";
          message = `${job.author.full_name} posted a new job: ${job.title} at ${job.company_name}`;
          break;
        case JobUpdateType.UPDATED:
          title = "Job Updated";
          message = `${job.author.full_name} updated the job: ${job.title}`;
          break;
        case JobUpdateType.DELETED:
          title = "Job Removed";
          message = `${job.author.full_name} removed the job: ${job.title}`;
          break;
        default:
          return;
      }

      const notification: SocketNotification = {
        userId: 0, // Will be set for each user
        tenantId,
        type: NotificationType.JOB_POSTED,
        title,
        message,
        data: { jobId, jobTitle: job.title, companyName: job.company_name, authorName: job.author.full_name },
        createdAt: new Date(),
      };

      // Send to all users in the tenant (except the author)
      const tenantUsers = await prisma.user.findMany({
        where: {
          tenant_id: tenantId,
          account_status: "APPROVED",
          id: { not: authorId },
        },
        select: { id: true },
      });

      const userIds = tenantUsers.map(user => user.id);
      await socketService.sendNotificationToUsers(userIds, notification);

      // Also send real-time job update
      const jobUpdate: JobUpdate = {
        jobId,
        tenantId,
        authorId,
        title: job.title,
        companyName: job.company_name,
        updateType: type,
        updatedBy: job.author.full_name,
      };

      await socketService.handleJobUpdate(jobUpdate);
      Logger.info(`Job notification sent for job ${jobId} (${type})`);
    } catch (error) {
      Logger.error("Error sending job notification:", error);
    }
  }

  // Send post-related notifications
  async sendPostNotification(postId: number, tenantId: number, type: PostUpdateType, authorId: number, targetUserId?: number) {
    try {
      const post = await prisma.generalPost.findUnique({
        where: { id: postId },
        include: { author: { select: { full_name: true } } },
      });

      if (!post) return;

      let title = "";
      let message = "";

      switch (type) {
        case PostUpdateType.LIKED:
          title = "Post Liked";
          message = `${post.author.full_name} liked your post`;
          break;
        case PostUpdateType.COMMENTED:
          title = "New Comment";
          message = `${post.author.full_name} commented on your post`;
          break;
        default:
          return;
      }

      if (targetUserId && targetUserId !== authorId) {
        const notification: SocketNotification = {
          userId: targetUserId,
          tenantId,
          type: type === PostUpdateType.LIKED ? NotificationType.POST_LIKED : NotificationType.POST_COMMENTED,
          title,
          message,
          data: { postId, postTitle: post.title, authorName: post.author.full_name },
          createdAt: new Date(),
        };

        await socketService.sendNotificationToUser(targetUserId, notification);
      }

      // Also send real-time post update
      const postUpdate: PostUpdate = {
        postId,
        tenantId,
        authorId,
        title: post.title,
        content: post.content,
        updateType: type,
        updatedBy: post.author.full_name,
      };

      await socketService.handlePostUpdate(postUpdate);
      Logger.info(`Post notification sent for post ${postId} (${type})`);
    } catch (error) {
      Logger.error("Error sending post notification:", error);
    }
  }

  // Send system notification to all users in tenant
  async sendSystemNotification(tenantId: number, title: string, message: string, data?: any) {
    try {
      const notification: SocketNotification = {
        userId: 0, // Will be set for each user
        tenantId,
        type: NotificationType.SYSTEM,
        title,
        message,
        data,
        createdAt: new Date(),
      };

      await socketService.sendNotificationToTenant(tenantId, notification);
      Logger.info(`System notification sent to tenant ${tenantId}: ${title}`);
    } catch (error) {
      Logger.error("Error sending system notification:", error);
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
export default notificationService;
