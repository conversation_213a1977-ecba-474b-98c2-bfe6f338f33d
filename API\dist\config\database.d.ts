import { PrismaClient, Prisma } from "@prisma/client";
declare global {
    var __prisma: PrismaClient | undefined;
    var __prismaReadReplica: PrismaClient | undefined;
}
declare const prisma: PrismaClient<Prisma.PrismaClientOptions, never, import("@prisma/client/runtime/library").DefaultArgs>;
export declare const checkDatabaseHealth: () => Promise<boolean>;
export declare const checkReadReplicaHealth: () => Promise<boolean>;
export declare const getDatabaseMetrics: () => Promise<{
    users: number;
    posts: number;
    queryTime: string;
    connection: {
        status: string;
        pingTime: string;
        hasReadReplica: boolean;
        timestamp: string;
        error?: never;
    } | {
        status: string;
        error: string;
        timestamp: string;
        pingTime?: never;
        hasReadReplica?: never;
    };
    timestamp: string;
} | null>;
export declare const getConnectionInfo: () => Promise<{
    status: string;
    pingTime: string;
    hasReadReplica: boolean;
    timestamp: string;
    error?: never;
} | {
    status: string;
    error: string;
    timestamp: string;
    pingTime?: never;
    hasReadReplica?: never;
}>;
export declare const withQueryPerformanceMonitoring: <T>(operation: string, queryFn: () => Promise<T>) => Promise<T>;
export declare const getReadClient: () => PrismaClient<Prisma.PrismaClientOptions, never, import("@prisma/client/runtime/library").DefaultArgs>;
export declare const getWriteClient: () => PrismaClient<Prisma.PrismaClientOptions, never, import("@prisma/client/runtime/library").DefaultArgs>;
export declare const gracefulDatabaseShutdown: () => Promise<void>;
export declare const getConnectionPoolStatus: () => {
    active: string;
    idle: string;
    total: string;
    waiting: string;
    note: string;
};
export { prisma };
//# sourceMappingURL=database.d.ts.map