import { Request, Response, NextFunction } from "express";
import { UserRole, UserStatus } from "@prisma/client";
import { AuthUtils } from "../utils/auth";
import { prisma } from "../config/database";
import { createError } from "./errorHandler";

// Extend Request interface for user property
interface AuthenticatedRequest extends Request {
  user?: {
    userId: string;
    email: string;
    role: UserRole;
    status: UserStatus;
    id: string;
    tenant_id: number;
  };
}

// Note: Request interface extension is defined in src/types/express.d.ts

/**
 * Middleware to authenticate JWT token
 */
export const authenticate = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    // Debug logging
    console.log("🔍 Auth Debug - Authorization header:", req.headers.authorization);
    console.log("🔍 Auth Debug - All headers:", JSON.stringify(req.headers, null, 2));

    const token = AuthUtils.extractTokenFromHeader(req.headers.authorization);
    console.log("🔍 Auth Debug - Extracted token:", token ? "Token found" : "No token");

    if (!token) {
      throw createError("Access token is required", 401);
    }

    const payload = AuthUtils.verifyAccessToken(token);

    // Verify user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: parseInt(payload.userId) },
      include: {
        tenant: {
          select: {
            is_active: true,
          },
        },
      },
    });

    if (!user || !user.tenant.is_active) {
      throw createError("User not found or tenant inactive", 401);
    }

    if (user.account_status === UserStatus.DEACTIVATED) {
      throw createError("Account has been deactivated", 403);
    }

    if (user.account_status === UserStatus.REJECTED) {
      throw createError("Account access has been denied", 403);
    }

    // Add user info to request
    req.user = {
      userId: payload.userId,
      email: user.email,
      role: user.role,
      status: user.account_status,
      id: user.id.toString(),
      tenant_id: user.tenant_id,
    };

    next();
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === "JsonWebTokenError") {
        return next(createError("Invalid token", 401));
      }
      if (error.name === "TokenExpiredError") {
        return next(createError("Token expired", 401));
      }
    }
    next(error);
  }
};

/**
 * Middleware to check if user is approved
 */
export const requireApproved = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (!req.user) {
    return next(createError("Authentication required", 401));
  }

  if (req.user.status !== UserStatus.APPROVED) {
    return next(createError("Account pending approval", 403));
  }

  next();
};

/**
 * Middleware to authorize specific roles
 */
export const authorize = (...roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(createError("Authentication required", 401));
    }

    if (!roles.includes(req.user.role as UserRole)) {
      return next(createError("Insufficient permissions", 403));
    }

    next();
  };
};

/**
 * Middleware to check if user is tenant admin or super admin
 */
export const requireTenantAdmin = authorize(UserRole.TENANT_ADMIN, UserRole.SUPER_ADMIN);

/**
 * Middleware to check if user is super admin
 */
export const requireSuperAdmin = authorize(UserRole.SUPER_ADMIN);

/**
 * Middleware to check if user is alumni
 */
export const requireAlumni = authorize(UserRole.ALUMNUS);

/**
 * Middleware to check if user is student
 */
export const requireStudent = authorize(UserRole.STUDENT);

/**
 * Middleware to check if user is alumni or admin
 */
export const requireAlumniOrAdmin = authorize(UserRole.ALUMNUS, UserRole.TENANT_ADMIN, UserRole.SUPER_ADMIN);

/**
 * Middleware to check if user is student or alumni (not admin-only)
 */
export const requireStudentOrAlumni = authorize(UserRole.STUDENT, UserRole.ALUMNUS);

/**
 * Optional authentication - doesn't fail if no token provided
 */
export const optionalAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const token = AuthUtils.extractTokenFromHeader(req.headers.authorization);

    if (token) {
      const payload = AuthUtils.verifyAccessToken(token);

      const user = await prisma.user.findUnique({
        where: { id: parseInt(payload.userId) },
        include: {
          tenant: {
            select: {
              is_active: true,
            },
          },
        },
      });

      if (user && user.account_status === UserStatus.APPROVED && user.tenant.is_active) {
        req.user = {
          userId: payload.userId,
          email: user.email,
          role: user.role,
          status: user.account_status,
          id: user.id.toString(),
          tenant_id: user.tenant_id,
        };
      }
    }
  } catch (error) {
    // Ignore authentication errors for optional auth
  }

  next();
};
