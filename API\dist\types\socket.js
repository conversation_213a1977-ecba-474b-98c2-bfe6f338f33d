"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityType = exports.RoomType = exports.PostUpdateType = exports.JobUpdateType = exports.EventUpdateType = exports.FollowStatus = exports.NotificationType = exports.MessageType = exports.UserStatus = void 0;
var UserStatus;
(function (UserStatus) {
    UserStatus["ONLINE"] = "online";
    UserStatus["AWAY"] = "away";
    UserStatus["BUSY"] = "busy";
    UserStatus["OFFLINE"] = "offline";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
var MessageType;
(function (MessageType) {
    MessageType["TEXT"] = "text";
    MessageType["IMAGE"] = "image";
    MessageType["FILE"] = "file";
    MessageType["SYSTEM"] = "system";
})(MessageType || (exports.MessageType = MessageType = {}));
var NotificationType;
(function (NotificationType) {
    NotificationType["FOLLOW_REQUEST"] = "follow_request";
    NotificationType["FOLLOW_ACCEPTED"] = "follow_accepted";
    NotificationType["CONNECTION_REQUEST"] = "connection_request";
    NotificationType["CONNECTION_ACCEPTED"] = "connection_accepted";
    NotificationType["MESSAGE"] = "message";
    NotificationType["EVENT_INVITATION"] = "event_invitation";
    NotificationType["EVENT_REMINDER"] = "event_reminder";
    NotificationType["JOB_POSTED"] = "job_posted";
    NotificationType["POST_LIKED"] = "post_liked";
    NotificationType["POST_COMMENTED"] = "post_commented";
    NotificationType["SYSTEM"] = "system";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
var FollowStatus;
(function (FollowStatus) {
    FollowStatus["PENDING"] = "PENDING";
    FollowStatus["ACCEPTED"] = "ACCEPTED";
    FollowStatus["REJECTED"] = "REJECTED";
    FollowStatus["BLOCKED"] = "BLOCKED";
})(FollowStatus || (exports.FollowStatus = FollowStatus = {}));
var EventUpdateType;
(function (EventUpdateType) {
    EventUpdateType["CREATED"] = "created";
    EventUpdateType["UPDATED"] = "updated";
    EventUpdateType["DELETED"] = "deleted";
    EventUpdateType["RSVP_ADDED"] = "rsvp_added";
    EventUpdateType["RSVP_REMOVED"] = "rsvp_removed";
})(EventUpdateType || (exports.EventUpdateType = EventUpdateType = {}));
var JobUpdateType;
(function (JobUpdateType) {
    JobUpdateType["POSTED"] = "posted";
    JobUpdateType["UPDATED"] = "updated";
    JobUpdateType["DELETED"] = "deleted";
    JobUpdateType["APPLICATION_RECEIVED"] = "application_received";
})(JobUpdateType || (exports.JobUpdateType = JobUpdateType = {}));
var PostUpdateType;
(function (PostUpdateType) {
    PostUpdateType["CREATED"] = "created";
    PostUpdateType["UPDATED"] = "updated";
    PostUpdateType["DELETED"] = "deleted";
    PostUpdateType["LIKED"] = "liked";
    PostUpdateType["UNLIKED"] = "unliked";
    PostUpdateType["COMMENTED"] = "commented";
})(PostUpdateType || (exports.PostUpdateType = PostUpdateType = {}));
var RoomType;
(function (RoomType) {
    RoomType["CHAT"] = "chat";
    RoomType["EVENT"] = "event";
    RoomType["JOB"] = "job";
    RoomType["POST"] = "post";
    RoomType["NOTIFICATION"] = "notification";
    RoomType["TENANT"] = "tenant";
})(RoomType || (exports.RoomType = RoomType = {}));
var ActivityType;
(function (ActivityType) {
    ActivityType["USER_JOINED"] = "user_joined";
    ActivityType["USER_LEFT"] = "user_left";
    ActivityType["MESSAGE_SENT"] = "message_sent";
    ActivityType["POST_CREATED"] = "post_created";
    ActivityType["EVENT_CREATED"] = "event_created";
    ActivityType["JOB_POSTED"] = "job_posted";
    ActivityType["FOLLOW_REQUEST_SENT"] = "follow_request_sent";
    ActivityType["CONNECTION_MADE"] = "connection_made";
})(ActivityType || (exports.ActivityType = ActivityType = {}));
//# sourceMappingURL=socket.js.map