"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SOCKET_ROOMS = exports.SOCKET_EVENTS = exports.getSocketIO = exports.initializeSocket = exports.socketConfig = void 0;
const socket_io_1 = require("socket.io");
const cors_1 = require("./cors");
exports.socketConfig = {
    cors: {
        origin: cors_1.corsOptions.origin,
        methods: ["GET", "POST"],
        credentials: true,
    },
    allowEIO3: true,
    transports: ["websocket", "polling"],
    pingTimeout: 60000,
    pingInterval: 25000,
    upgradeTimeout: 30000,
    maxHttpBufferSize: 1e6,
    allowRequest: (req, callback) => {
        callback(null, true);
    },
};
let io = null;
const initializeSocket = (server) => {
    if (io) {
        return io;
    }
    io = new socket_io_1.Server(server, exports.socketConfig);
    console.log("🔌 Socket.IO server initialized");
    return io;
};
exports.initializeSocket = initializeSocket;
const getSocketIO = () => {
    if (!io) {
        throw new Error("Socket.IO server not initialized. Call initializeSocket first.");
    }
    return io;
};
exports.getSocketIO = getSocketIO;
exports.SOCKET_EVENTS = {
    CONNECTION: "connection",
    DISCONNECT: "disconnect",
    CONNECT_ERROR: "connect_error",
    AUTHENTICATE: "authenticate",
    AUTHENTICATED: "authenticated",
    AUTHENTICATION_ERROR: "authentication_error",
    USER_ONLINE: "user_online",
    USER_OFFLINE: "user_offline",
    USER_STATUS_CHANGE: "user_status_change",
    GET_ONLINE_USERS: "get_online_users",
    ONLINE_USERS_LIST: "online_users_list",
    SEND_MESSAGE: "send_message",
    RECEIVE_MESSAGE: "receive_message",
    MESSAGE_DELIVERED: "message_delivered",
    MESSAGE_READ: "message_read",
    TYPING_START: "typing_start",
    TYPING_STOP: "typing_stop",
    TYPING_INDICATOR: "typing_indicator",
    SEND_NOTIFICATION: "send_notification",
    RECEIVE_NOTIFICATION: "receive_notification",
    NOTIFICATION_READ: "notification_read",
    NOTIFICATION_COUNT: "notification_count",
    FOLLOW_REQUEST: "follow_request",
    FOLLOW_ACCEPTED: "follow_accepted",
    FOLLOW_REJECTED: "follow_rejected",
    CONNECTION_REQUEST: "connection_request",
    CONNECTION_ACCEPTED: "connection_accepted",
    CONNECTION_REJECTED: "connection_rejected",
    EVENT_CREATED: "event_created",
    EVENT_UPDATED: "event_updated",
    EVENT_DELETED: "event_deleted",
    EVENT_RSVP: "event_rsvp",
    JOB_POSTED: "job_posted",
    JOB_UPDATED: "job_updated",
    JOB_DELETED: "job_deleted",
    JOB_APPLICATION: "job_application",
    POST_CREATED: "post_created",
    POST_UPDATED: "post_updated",
    POST_DELETED: "post_deleted",
    POST_LIKED: "post_liked",
    POST_COMMENTED: "post_commented",
    JOIN_ROOM: "join_room",
    LEAVE_ROOM: "leave_room",
    ROOM_MESSAGE: "room_message",
    ERROR: "error",
    VALIDATION_ERROR: "validation_error",
};
exports.SOCKET_ROOMS = {
    TENANT: (tenantId) => `tenant:${tenantId}`,
    USER: (userId) => `user:${userId}`,
    CHAT: (chatId) => `chat:${chatId}`,
    PRIVATE_CHAT: (userId1, userId2) => {
        const sortedIds = [userId1, userId2].sort();
        return `private:${sortedIds[0]}:${sortedIds[1]}`;
    },
    EVENT: (eventId) => `event:${eventId}`,
    JOB: (jobId) => `job:${jobId}`,
    POST: (postId) => `post:${postId}`,
    NOTIFICATIONS: (userId) => `notifications:${userId}`,
    ONLINE_USERS: (tenantId) => `online:${tenantId}`,
};
//# sourceMappingURL=socket.js.map