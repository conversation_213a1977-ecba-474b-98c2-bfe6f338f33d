"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const upload_1 = require("../middleware/upload");
const userController = __importStar(require("../controllers/userController"));
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get("/profile", userController.getProfile);
router.put("/profile", validation_1.updateProfileValidation, userController.updateProfile);
router.post("/profile/picture", upload_1.uploadProfilePicture, upload_1.handleUploadError, userController.uploadProfilePicture);
router.get("/directory", auth_1.requireApproved, validation_1.paginationValidation, userController.getUserDirectory);
router.get("/:id", auth_1.requireApproved, validation_1.idValidation, userController.getUserById);
router.get("/connections/list", auth_1.requireApproved, validation_1.paginationValidation, userController.getConnections);
router.post("/connections/request", auth_1.requireApproved, validation_1.connectionRequestValidation, userController.sendConnectionRequest);
router.put("/connections/:id/respond", auth_1.requireApproved, validation_1.idValidation, validation_1.connectionResponseValidation, userController.respondToConnection);
router.get("/connections/requests", auth_1.requireApproved, validation_1.paginationValidation, userController.getConnectionRequests);
exports.default = router;
//# sourceMappingURL=user.js.map