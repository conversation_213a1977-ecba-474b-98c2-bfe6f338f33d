"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeSocketHandlers = void 0;
const client_1 = require("@prisma/client");
const socket_1 = require("../config/socket");
const socketAuth_1 = require("../middleware/socketAuth");
const socketService_1 = require("../services/socketService");
const socket_2 = require("../types/socket");
const loggerService_1 = require("../services/loggerService");
const prisma = new client_1.PrismaClient();
const initializeSocketHandlers = (io) => {
    socketService_1.socketService.initialize(io);
    io.use(socketAuth_1.socketAuthMiddleware);
    io.on(socket_1.SOCKET_EVENTS.CONNECTION, (socket) => {
        loggerService_1.Logger.info(`Socket connected: ${socket.id} for user ${socket.userId}`);
        socketService_1.socketService.handleUserConnection(socket);
        socket.on(socket_1.SOCKET_EVENTS.AUTHENTICATE, async (data) => {
            try {
                socket.emit(socket_1.SOCKET_EVENTS.AUTHENTICATED, {
                    userId: socket.userId,
                    tenantId: socket.tenantId,
                    timestamp: new Date(),
                });
            }
            catch (error) {
                (0, socketAuth_1.emitSocketError)(socket, {
                    code: "AUTH_ERROR",
                    message: "Authentication failed",
                    details: error,
                });
            }
        });
        setupPresenceHandlers(socket);
        setupMessagingHandlers(socket);
        setupNotificationHandlers(socket);
        setupRoomHandlers(socket);
        setupFollowHandlers(socket);
        setupUpdateHandlers(socket);
        socket.on(socket_1.SOCKET_EVENTS.DISCONNECT, (reason) => {
            loggerService_1.Logger.info(`Socket disconnected: ${socket.id} for user ${socket.userId}, reason: ${reason}`);
            socketService_1.socketService.handleUserDisconnection(socket);
        });
        socket.on("error", (error) => {
            loggerService_1.Logger.error(`Socket error for ${socket.id}:`, error);
        });
    });
    loggerService_1.Logger.info("Socket handlers initialized");
};
exports.initializeSocketHandlers = initializeSocketHandlers;
const setupPresenceHandlers = (socket) => {
    socket.on(socket_1.SOCKET_EVENTS.GET_ONLINE_USERS, async () => {
        try {
            if (!socket.tenantId)
                return;
            const onlineUsers = socketService_1.socketService.getOnlineUsersForTenant(socket.tenantId);
            socket.emit(socket_1.SOCKET_EVENTS.ONLINE_USERS_LIST, onlineUsers);
        }
        catch (error) {
            (0, socketAuth_1.emitSocketError)(socket, {
                code: "PRESENCE_ERROR",
                message: "Failed to get online users",
                details: error,
            });
        }
    });
    socket.on(socket_1.SOCKET_EVENTS.USER_STATUS_CHANGE, async (data) => {
        try {
            if (!socket.userId || !Object.values(socket_2.UserStatus).includes(data.status)) {
                return (0, socketAuth_1.emitValidationError)(socket, "Invalid status value");
            }
            await socketService_1.socketService.updateUserStatus(socket.userId, data.status);
        }
        catch (error) {
            (0, socketAuth_1.emitSocketError)(socket, {
                code: "STATUS_UPDATE_ERROR",
                message: "Failed to update user status",
                details: error,
            });
        }
    });
};
const setupMessagingHandlers = (socket) => {
    socket.on(socket_1.SOCKET_EVENTS.SEND_MESSAGE, async (data) => {
        try {
            if (!socket.userId || !data.receiverId || !data.content?.trim()) {
                return (0, socketAuth_1.emitValidationError)(socket, "Invalid message data");
            }
            const receiver = await prisma.user.findFirst({
                where: {
                    id: data.receiverId,
                    tenant_id: socket.tenantId,
                    account_status: "APPROVED",
                },
            });
            if (!receiver) {
                return (0, socketAuth_1.emitValidationError)(socket, "Receiver not found or not accessible");
            }
            const message = {
                id: `msg_${Date.now()}_${socket.userId}`,
                senderId: socket.userId,
                receiverId: data.receiverId,
                content: data.content.trim(),
                messageType: data.messageType || "text",
                timestamp: new Date(),
                isRead: false,
                isDelivered: false,
            };
            await socketService_1.socketService.sendPrivateMessage(socket.userId, data.receiverId, message);
        }
        catch (error) {
            (0, socketAuth_1.emitSocketError)(socket, {
                code: "MESSAGE_SEND_ERROR",
                message: "Failed to send message",
                details: error,
            });
        }
    });
    socket.on(socket_1.SOCKET_EVENTS.TYPING_START, (data) => {
        try {
            if (!socket.userId || (!data.receiverId && !data.roomId)) {
                return (0, socketAuth_1.emitValidationError)(socket, "Invalid typing indicator data");
            }
            const indicator = {
                userId: socket.userId,
                userName: "User",
                receiverId: data.receiverId,
                roomId: data.roomId || undefined,
                isTyping: true,
            };
            socketService_1.socketService.handleTypingIndicator(indicator);
        }
        catch (error) {
            (0, socketAuth_1.emitSocketError)(socket, {
                code: "TYPING_ERROR",
                message: "Failed to handle typing indicator",
                details: error,
            });
        }
    });
    socket.on(socket_1.SOCKET_EVENTS.TYPING_STOP, (data) => {
        try {
            if (!socket.userId || (!data.receiverId && !data.roomId)) {
                return (0, socketAuth_1.emitValidationError)(socket, "Invalid typing indicator data");
            }
            const indicator = {
                userId: socket.userId,
                userName: "User",
                receiverId: data.receiverId,
                roomId: data.roomId || undefined,
                isTyping: false,
            };
            socketService_1.socketService.handleTypingIndicator(indicator);
        }
        catch (error) {
            (0, socketAuth_1.emitSocketError)(socket, {
                code: "TYPING_ERROR",
                message: "Failed to handle typing indicator",
                details: error,
            });
        }
    });
    socket.on(socket_1.SOCKET_EVENTS.MESSAGE_READ, async (data) => {
        try {
            if (!socket.userId || !data.messageId || !data.senderId) {
                return (0, socketAuth_1.emitValidationError)(socket, "Invalid message read data");
            }
            socket.to(socket_1.SOCKET_ROOMS.USER(data.senderId)).emit(socket_1.SOCKET_EVENTS.MESSAGE_READ, {
                messageId: data.messageId,
                readBy: socket.userId,
                readAt: new Date(),
            });
        }
        catch (error) {
            (0, socketAuth_1.emitSocketError)(socket, {
                code: "MESSAGE_READ_ERROR",
                message: "Failed to mark message as read",
                details: error,
            });
        }
    });
};
const setupNotificationHandlers = (socket) => {
    socket.on(socket_1.SOCKET_EVENTS.NOTIFICATION_READ, async (data) => {
        try {
            if (!socket.userId || !data.notificationId) {
                return (0, socketAuth_1.emitValidationError)(socket, "Invalid notification data");
            }
            socket.emit(socket_1.SOCKET_EVENTS.NOTIFICATION_READ, {
                notificationId: data.notificationId,
                readAt: new Date(),
            });
        }
        catch (error) {
            (0, socketAuth_1.emitSocketError)(socket, {
                code: "NOTIFICATION_ERROR",
                message: "Failed to mark notification as read",
                details: error,
            });
        }
    });
};
const setupRoomHandlers = (socket) => {
    socket.on(socket_1.SOCKET_EVENTS.JOIN_ROOM, async (data) => {
        try {
            if (!data.roomId || !data.roomType) {
                return (0, socketAuth_1.emitValidationError)(socket, "Invalid room data");
            }
            await socketService_1.socketService.joinRoom(socket, data.roomId, data.roomType);
            socket.emit(socket_1.SOCKET_EVENTS.JOIN_ROOM, {
                roomId: data.roomId,
                roomType: data.roomType,
                joinedAt: new Date(),
            });
        }
        catch (error) {
            (0, socketAuth_1.emitSocketError)(socket, {
                code: "ROOM_JOIN_ERROR",
                message: "Failed to join room",
                details: error,
            });
        }
    });
    socket.on(socket_1.SOCKET_EVENTS.LEAVE_ROOM, async (data) => {
        try {
            if (!data.roomId || !data.roomType) {
                return (0, socketAuth_1.emitValidationError)(socket, "Invalid room data");
            }
            await socketService_1.socketService.leaveRoom(socket, data.roomId, data.roomType);
            socket.emit(socket_1.SOCKET_EVENTS.LEAVE_ROOM, {
                roomId: data.roomId,
                roomType: data.roomType,
                leftAt: new Date(),
            });
        }
        catch (error) {
            (0, socketAuth_1.emitSocketError)(socket, {
                code: "ROOM_LEAVE_ERROR",
                message: "Failed to leave room",
                details: error,
            });
        }
    });
};
const setupFollowHandlers = (socket) => {
    socket.on(socket_1.SOCKET_EVENTS.FOLLOW_REQUEST, async (data) => {
        try {
            if (!socket.userId || !data.followingId || socket.userId === data.followingId) {
                return (0, socketAuth_1.emitValidationError)(socket, "Invalid follow request data");
            }
            const targetUser = await prisma.user.findFirst({
                where: {
                    id: data.followingId,
                    tenant_id: socket.tenantId,
                    account_status: "APPROVED",
                },
                select: { id: true, full_name: true },
            });
            if (!targetUser) {
                return (0, socketAuth_1.emitValidationError)(socket, "Target user not found");
            }
            const existingFollow = await prisma.follow.findUnique({
                where: {
                    follower_id_following_id: {
                        follower_id: socket.userId,
                        following_id: data.followingId,
                    },
                },
            });
            if (existingFollow) {
                return (0, socketAuth_1.emitValidationError)(socket, "Follow request already exists");
            }
            const followRequest = await prisma.follow.create({
                data: {
                    follower_id: socket.userId,
                    following_id: data.followingId,
                    tenant_id: socket.tenantId,
                    status: "PENDING",
                },
                include: {
                    follower: { select: { full_name: true } },
                },
            });
            await socketService_1.socketService.handleFollowRequest({
                id: followRequest.follower_id,
                followerId: socket.userId,
                followingId: data.followingId,
                tenantId: socket.tenantId,
                status: "PENDING",
                followerName: followRequest.follower.full_name,
                createdAt: followRequest.created_at,
            });
            socket.emit(socket_1.SOCKET_EVENTS.FOLLOW_REQUEST, {
                success: true,
                followingId: data.followingId,
                status: "PENDING",
            });
        }
        catch (error) {
            (0, socketAuth_1.emitSocketError)(socket, {
                code: "FOLLOW_REQUEST_ERROR",
                message: "Failed to send follow request",
                details: error,
            });
        }
    });
    socket.on(socket_1.SOCKET_EVENTS.FOLLOW_ACCEPTED, async (data) => {
        try {
            if (!socket.userId || !data.followerId || !["accept", "reject"].includes(data.action)) {
                return (0, socketAuth_1.emitValidationError)(socket, "Invalid follow response data");
            }
            const followRequest = await prisma.follow.findUnique({
                where: {
                    follower_id_following_id: {
                        follower_id: data.followerId,
                        following_id: socket.userId,
                    },
                },
                include: {
                    following: { select: { full_name: true } },
                },
            });
            if (!followRequest || followRequest.status !== "PENDING") {
                return (0, socketAuth_1.emitValidationError)(socket, "Follow request not found or already processed");
            }
            const newStatus = data.action === "accept" ? "ACCEPTED" : "REJECTED";
            await prisma.follow.update({
                where: {
                    follower_id_following_id: {
                        follower_id: data.followerId,
                        following_id: socket.userId,
                    },
                },
                data: { status: newStatus },
            });
            const notification = {
                userId: data.followerId,
                tenantId: socket.tenantId,
                type: data.action === "accept" ? socket_2.NotificationType.FOLLOW_ACCEPTED : socket_2.NotificationType.FOLLOW_ACCEPTED,
                title: data.action === "accept" ? "Follow Request Accepted" : "Follow Request Rejected",
                message: `${followRequest.following.full_name} ${data.action}ed your follow request`,
                createdAt: new Date(),
            };
            await socketService_1.socketService.sendNotificationToUser(data.followerId, notification);
            socket.emit(data.action === "accept" ? socket_1.SOCKET_EVENTS.FOLLOW_ACCEPTED : socket_1.SOCKET_EVENTS.FOLLOW_REJECTED, {
                followerId: data.followerId,
                action: data.action,
                timestamp: new Date(),
            });
        }
        catch (error) {
            (0, socketAuth_1.emitSocketError)(socket, {
                code: "FOLLOW_RESPONSE_ERROR",
                message: "Failed to respond to follow request",
                details: error,
            });
        }
    });
};
const setupUpdateHandlers = (socket) => {
    socket.on("internal:event_update", (eventUpdate) => {
        socketService_1.socketService.handleEventUpdate(eventUpdate);
    });
    socket.on("internal:job_update", (jobUpdate) => {
        socketService_1.socketService.handleJobUpdate(jobUpdate);
    });
    socket.on("internal:post_update", (postUpdate) => {
        socketService_1.socketService.handlePostUpdate(postUpdate);
    });
};
//# sourceMappingURL=socketHandlers.js.map