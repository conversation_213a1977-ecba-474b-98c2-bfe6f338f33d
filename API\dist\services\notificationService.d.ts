import { EventUpdateType, JobUpdateType, PostUpdateType } from "../types/socket";
declare class NotificationService {
    sendFollowRequestNotification(followerId: number, followingId: number, tenantId: number): Promise<void>;
    sendFollowAcceptedNotification(followerId: number, followingId: number, tenantId: number): Promise<void>;
    sendConnectionRequestNotification(requesterId: number, targetId: number, tenantId: number): Promise<void>;
    sendConnectionAcceptedNotification(requesterId: number, accepterId: number, tenantId: number): Promise<void>;
    sendMessageNotification(senderId: number, receiverId: number, tenantId: number, messageContent: string): Promise<void>;
    sendEventNotification(eventId: number, tenantId: number, type: EventUpdateType, authorId: number): Promise<void>;
    sendJobNotification(jobId: number, tenantId: number, type: JobUpdateType, authorId: number): Promise<void>;
    sendPostNotification(postId: number, tenantId: number, type: PostUpdateType, authorId: number, targetUserId?: number): Promise<void>;
    sendSystemNotification(tenantId: number, title: string, message: string, data?: any): Promise<void>;
}
export declare const notificationService: NotificationService;
export default notificationService;
//# sourceMappingURL=notificationService.d.ts.map