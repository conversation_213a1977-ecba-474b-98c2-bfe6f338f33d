const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDatabaseIntegration() {
  console.log('🗄️  Testing Database Integration...\n');

  try {
    console.log('1. Checking database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful');

    console.log('\n2. Checking UserProfile table structure...');
    const tableInfo = await prisma.$queryRaw`
      DESCRIBE user_profiles
    `;
    
    console.log('✅ UserProfile table structure:');
    tableInfo.forEach(column => {
      console.log(`   - ${column.Field}: ${column.Type} ${column.Null === 'YES' ? '(nullable)' : '(required)'}`);
    });

    // Check if profile_picture_url field exists
    const profilePictureField = tableInfo.find(col => col.Field === 'profile_picture_url');
    if (profilePictureField) {
      console.log('✅ profile_picture_url field exists');
      console.log(`   Type: ${profilePictureField.Type}`);
      console.log(`   Nullable: ${profilePictureField.Null === 'YES' ? 'Yes' : 'No'}`);
    } else {
      console.log('❌ profile_picture_url field missing');
    }

    console.log('\n3. Checking existing user profiles...');
    const profiles = await prisma.userProfile.findMany({
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true,
            role: true
          }
        }
      }
    });

    console.log(`✅ Found ${profiles.length} user profiles:`);
    profiles.forEach((profile, index) => {
      console.log(`\n   ${index + 1}. User: ${profile.user.full_name} (${profile.user.email})`);
      console.log(`      Role: ${profile.user.role}`);
      console.log(`      Profile Picture: ${profile.profile_picture_url || 'None'}`);
      console.log(`      Updated: ${profile.updated_at}`);
    });

    console.log('\n4. Testing profile picture URL updates...');
    const testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (testUser) {
      console.log('✅ Found test user:', testUser.full_name);
      
      // Test upsert functionality
      const testImageUrl = '/uploads/profiles/test-image-123.png';
      
      const updatedProfile = await prisma.userProfile.upsert({
        where: { user_id: testUser.id },
        update: { 
          profile_picture_url: testImageUrl,
          updated_at: new Date()
        },
        create: {
          user_id: testUser.id,
          tenant_id: testUser.tenant_id,
          profile_picture_url: testImageUrl,
        }
      });

      console.log('✅ Profile upsert successful');
      console.log(`   Profile Picture URL: ${updatedProfile.profile_picture_url}`);
      console.log(`   Updated At: ${updatedProfile.updated_at}`);

      // Verify the update
      const verifyProfile = await prisma.userProfile.findUnique({
        where: { user_id: testUser.id }
      });

      if (verifyProfile && verifyProfile.profile_picture_url === testImageUrl) {
        console.log('✅ Database update verified');
      } else {
        console.log('❌ Database update verification failed');
      }

    } else {
      console.log('⚠️  Test user not found');
    }

    console.log('\n5. Testing database constraints...');
    
    // Test unique constraint
    try {
      await prisma.userProfile.create({
        data: {
          user_id: testUser.id, // Duplicate user_id
          tenant_id: testUser.tenant_id,
          profile_picture_url: '/test/duplicate.png'
        }
      });
      console.log('❌ Unique constraint should have prevented duplicate');
    } catch (error) {
      if (error.code === 'P2002') {
        console.log('✅ Unique constraint working correctly');
      } else {
        console.log('⚠️  Unexpected error:', error.message);
      }
    }

    console.log('\n6. Testing foreign key relationships...');
    const profileWithUser = await prisma.userProfile.findFirst({
      include: {
        user: true
      }
    });

    if (profileWithUser) {
      console.log('✅ Foreign key relationship working');
      console.log(`   Profile belongs to: ${profileWithUser.user.full_name}`);
      console.log(`   User ID: ${profileWithUser.user.id}`);
      console.log(`   Tenant ID: ${profileWithUser.tenant_id}`);
    }

    console.log('\n7. Testing query performance...');
    const startTime = Date.now();
    
    const profilesWithUsers = await prisma.userProfile.findMany({
      include: {
        user: {
          select: {
            id: true,
            full_name: true,
            email: true
          }
        }
      },
      where: {
        profile_picture_url: {
          not: null
        }
      }
    });
    
    const endTime = Date.now();
    console.log(`✅ Query completed in ${endTime - startTime}ms`);
    console.log(`   Found ${profilesWithUsers.length} profiles with pictures`);

    console.log('\n🎉 Database integration test completed successfully!');
    
    console.log('\n📋 Database Integration Summary:');
    console.log('✅ Database connection working');
    console.log('✅ UserProfile table structure correct');
    console.log('✅ profile_picture_url field exists');
    console.log('✅ CRUD operations working');
    console.log('✅ Unique constraints enforced');
    console.log('✅ Foreign key relationships working');
    console.log('✅ Query performance acceptable');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.code) {
      console.error('Error code:', error.code);
    }
  } finally {
    await prisma.$disconnect();
  }
}

testDatabaseIntegration();
