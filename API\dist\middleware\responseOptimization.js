"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paginationOptimization = exports.cacheHeaders = exports.responseTransformation = exports.responseCompression = exports.fieldFiltering = void 0;
const loggerService_1 = require("../services/loggerService");
const OPTIMIZATION_CONFIG = {
    minifyJson: process.env.MINIFY_JSON !== "false",
    enableFieldFiltering: process.env.ENABLE_FIELD_FILTERING !== "false",
    enableTransformation: process.env.ENABLE_TRANSFORMATION !== "false",
    maxResponseSize: parseInt(process.env.MAX_RESPONSE_SIZE || "10485760"),
    enableCacheHeaders: process.env.ENABLE_CACHE_HEADERS !== "false",
};
const fieldFiltering = () => {
    return (req, res, next) => {
        if (!OPTIMIZATION_CONFIG.enableFieldFiltering) {
            return next();
        }
        const fields = req.query.fields;
        const exclude = req.query.exclude;
        if (!fields && !exclude) {
            return next();
        }
        const originalJson = res.json.bind(res);
        res.json = function (data) {
            try {
                const filteredData = applyFieldFiltering(data, fields, exclude);
                return originalJson(filteredData);
            }
            catch (error) {
                loggerService_1.Logger.error("Field filtering failed", {
                    error: error instanceof Error ? error.message : String(error),
                    fields,
                    exclude,
                    path: req.path,
                });
                return originalJson(data);
            }
        };
        next();
    };
};
exports.fieldFiltering = fieldFiltering;
function applyFieldFiltering(data, fields, exclude) {
    if (!data || typeof data !== "object") {
        return data;
    }
    if (Array.isArray(data)) {
        return data.map((item) => applyFieldFiltering(item, fields, exclude));
    }
    let result = { ...data };
    if (fields) {
        const includeFields = fields.split(",").map((f) => f.trim());
        const filteredResult = {};
        for (const field of includeFields) {
            if (field.includes(".")) {
                const [parent, ...nested] = field.split(".");
                if (parent && result[parent]) {
                    if (!filteredResult[parent]) {
                        filteredResult[parent] = {};
                    }
                    const nestedField = nested.join(".");
                    filteredResult[parent] = {
                        ...filteredResult[parent],
                        ...applyFieldFiltering(result[parent], nestedField),
                    };
                }
            }
            else if (result.hasOwnProperty(field)) {
                filteredResult[field] = result[field];
            }
        }
        result = filteredResult;
    }
    if (exclude) {
        const excludeFields = exclude.split(",").map((f) => f.trim());
        for (const field of excludeFields) {
            if (field.includes(".")) {
                const [parent, ...nested] = field.split(".");
                if (parent && result[parent]) {
                    const nestedField = nested.join(".");
                    result[parent] = applyFieldFiltering(result[parent], undefined, nestedField);
                }
            }
            else {
                delete result[field];
            }
        }
    }
    return result;
}
const responseCompression = () => {
    return (req, res, next) => {
        const originalJson = res.json.bind(res);
        res.json = function (data) {
            try {
                let processedData = data;
                if (OPTIMIZATION_CONFIG.minifyJson) {
                    processedData = removeEmptyValues(processedData);
                }
                const responseSize = JSON.stringify(processedData).length;
                if (responseSize > OPTIMIZATION_CONFIG.maxResponseSize) {
                    loggerService_1.Logger.warn("Large response detected", {
                        size: `${Math.round(responseSize / 1024 / 1024)}MB`,
                        path: req.path,
                        method: req.method,
                    });
                }
                return originalJson(processedData);
            }
            catch (error) {
                loggerService_1.Logger.error("Response compression failed", {
                    error: error instanceof Error ? error.message : String(error),
                    path: req.path,
                });
                return originalJson(data);
            }
        };
        next();
    };
};
exports.responseCompression = responseCompression;
function removeEmptyValues(obj) {
    if (obj === null || obj === undefined) {
        return undefined;
    }
    if (Array.isArray(obj)) {
        const filtered = obj.map((item) => removeEmptyValues(item)).filter((item) => item !== undefined);
        return filtered.length > 0 ? filtered : undefined;
    }
    if (typeof obj === "object") {
        const cleaned = {};
        let hasValues = false;
        for (const [key, value] of Object.entries(obj)) {
            const cleanedValue = removeEmptyValues(value);
            if (cleanedValue !== undefined) {
                cleaned[key] = cleanedValue;
                hasValues = true;
            }
        }
        return hasValues ? cleaned : undefined;
    }
    return obj;
}
const responseTransformation = () => {
    return (req, res, next) => {
        if (!OPTIMIZATION_CONFIG.enableTransformation) {
            return next();
        }
        const transform = req.query.transform;
        if (!transform) {
            return next();
        }
        const originalJson = res.json.bind(res);
        res.json = function (data) {
            try {
                const transformedData = applyTransformation(data, transform);
                return originalJson(transformedData);
            }
            catch (error) {
                loggerService_1.Logger.error("Response transformation failed", {
                    error: error instanceof Error ? error.message : String(error),
                    transform,
                    path: req.path,
                });
                return originalJson(data);
            }
        };
        next();
    };
};
exports.responseTransformation = responseTransformation;
function applyTransformation(data, transform) {
    switch (transform.toLowerCase()) {
        case "flatten":
            return flattenObject(data);
        case "camelcase":
            return convertKeysToCamelCase(data);
        case "snakecase":
            return convertKeysToSnakeCase(data);
        case "compact":
            return removeEmptyValues(data);
        default:
            loggerService_1.Logger.warn(`Unknown transformation: ${transform}`);
            return data;
    }
}
function flattenObject(obj, prefix = "") {
    if (!obj || typeof obj !== "object" || Array.isArray(obj)) {
        return obj;
    }
    const flattened = {};
    for (const [key, value] of Object.entries(obj)) {
        const newKey = prefix ? `${prefix}.${key}` : key;
        if (value && typeof value === "object" && !Array.isArray(value)) {
            Object.assign(flattened, flattenObject(value, newKey));
        }
        else {
            flattened[newKey] = value;
        }
    }
    return flattened;
}
function convertKeysToCamelCase(obj) {
    if (!obj || typeof obj !== "object") {
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map((item) => convertKeysToCamelCase(item));
    }
    const converted = {};
    for (const [key, value] of Object.entries(obj)) {
        const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        converted[camelKey] = convertKeysToCamelCase(value);
    }
    return converted;
}
function convertKeysToSnakeCase(obj) {
    if (!obj || typeof obj !== "object") {
        return obj;
    }
    if (Array.isArray(obj)) {
        return obj.map((item) => convertKeysToSnakeCase(item));
    }
    const converted = {};
    for (const [key, value] of Object.entries(obj)) {
        const snakeKey = key.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
        converted[snakeKey] = convertKeysToSnakeCase(value);
    }
    return converted;
}
const cacheHeaders = (options = {}) => {
    return (req, res, next) => {
        if (!OPTIMIZATION_CONFIG.enableCacheHeaders) {
            return next();
        }
        const { maxAge = 300, private: isPrivate = false, noCache = false, mustRevalidate = false, } = options;
        const originalStatus = res.status.bind(res);
        res.status = function (code) {
            if (code >= 400) {
                res.set("Cache-Control", "no-cache, no-store, must-revalidate");
                res.set("Pragma", "no-cache");
                res.set("Expires", "0");
            }
            return originalStatus(code);
        };
        if (req.method === "GET") {
            let cacheControl = [];
            if (noCache) {
                cacheControl.push("no-cache");
            }
            else {
                if (isPrivate) {
                    cacheControl.push("private");
                }
                else {
                    cacheControl.push("public");
                }
                cacheControl.push(`max-age=${maxAge}`);
            }
            if (mustRevalidate) {
                cacheControl.push("must-revalidate");
            }
            res.set("Cache-Control", cacheControl.join(", "));
            const originalJson = res.json.bind(res);
            res.json = function (data) {
                const etag = generateETag(data);
                res.set("ETag", etag);
                if (req.get("If-None-Match") === etag) {
                    return res.status(304).end();
                }
                return originalJson(data);
            };
        }
        next();
    };
};
exports.cacheHeaders = cacheHeaders;
function generateETag(data) {
    const crypto = require("crypto");
    const content = JSON.stringify(data);
    return `"${crypto.createHash("md5").update(content).digest("hex")}"`;
}
const paginationOptimization = () => {
    return (req, res, next) => {
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 20, 100);
        const offset = (page - 1) * limit;
        req.pagination = {
            page,
            limit,
            offset,
        };
        const originalJson = res.json.bind(res);
        res.json = function (data) {
            if (data && typeof data === "object" && data.items && data.total !== undefined) {
                const totalPages = Math.ceil(data.total / limit);
                const paginatedResponse = {
                    data: data.items,
                    pagination: {
                        page,
                        limit,
                        total: data.total,
                        totalPages,
                        hasNext: page < totalPages,
                        hasPrev: page > 1,
                        nextPage: page < totalPages ? page + 1 : null,
                        prevPage: page > 1 ? page - 1 : null,
                        nextUrl: undefined,
                        prevUrl: undefined,
                    },
                };
                const baseUrl = `${req.protocol}://${req.get("host")}${req.path}`;
                const queryParams = new URLSearchParams(req.query);
                if (page < totalPages) {
                    queryParams.set("page", (page + 1).toString());
                    paginatedResponse.pagination.nextUrl = `${baseUrl}?${queryParams}`;
                }
                if (page > 1) {
                    queryParams.set("page", (page - 1).toString());
                    paginatedResponse.pagination.prevUrl = `${baseUrl}?${queryParams}`;
                }
                return originalJson(paginatedResponse);
            }
            return originalJson(data);
        };
        next();
    };
};
exports.paginationOptimization = paginationOptimization;
//# sourceMappingURL=responseOptimization.js.map