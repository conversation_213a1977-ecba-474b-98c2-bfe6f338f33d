"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationPreferencesValidation = exports.sendMessageValidation = exports.createPostValidation = exports.connectionResponseValidation = exports.connectionRequestValidation = exports.paginationValidation = exports.idValidation = exports.updateProfileValidation = exports.refreshTokenValidation = exports.loginValidation = exports.registerValidation = exports.handleValidationErrors = void 0;
const express_validator_1 = require("express-validator");
const client_1 = require("@prisma/client");
const auth_1 = require("../utils/auth");
const handleValidationErrors = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map((error) => ({
            field: error.type === "field" ? error.path : "unknown",
            message: error.msg,
            value: error.type === "field" ? error.value : undefined,
        }));
        res.status(400).json({
            error: "Validation failed",
            details: errorMessages,
            timestamp: new Date().toISOString(),
        });
        return;
    }
    next();
};
exports.handleValidationErrors = handleValidationErrors;
exports.registerValidation = [
    (0, express_validator_1.body)("email").isEmail().normalizeEmail().withMessage("Please provide a valid email address"),
    (0, express_validator_1.body)("password")
        .isLength({ min: 8 })
        .withMessage("Password must be at least 8 characters long")
        .custom((password) => {
        const validation = auth_1.AuthUtils.validatePassword(password);
        if (!validation.isValid) {
            throw new Error(validation.errors.join(", "));
        }
        return true;
    }),
    (0, express_validator_1.body)("full_name").trim().isLength({ min: 2, max: 100 }).withMessage("Full name must be between 2 and 100 characters"),
    (0, express_validator_1.body)("mobile_number").optional().isMobilePhone("any").withMessage("Please provide a valid mobile number"),
    (0, express_validator_1.body)("tenant_id").isInt({ min: 1 }).withMessage("Valid tenant ID is required"),
    (0, express_validator_1.body)("usn")
        .optional()
        .trim()
        .isLength({ min: 6, max: 20 })
        .withMessage("USN must be between 6 and 20 characters")
        .custom((usn, { req }) => {
        if (usn && !auth_1.AuthUtils.validateUSN(usn)) {
            throw new Error("Invalid USN format");
        }
        const role = req.body.role;
        const isAdmin = role === client_1.UserRole.TENANT_ADMIN || role === client_1.UserRole.SUPER_ADMIN;
        if (!isAdmin && !usn) {
            throw new Error("USN is required for students and alumni");
        }
        return true;
    }),
    (0, express_validator_1.body)("course_name")
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage("Course name must be between 2 and 100 characters")
        .custom((course_name, { req }) => {
        const role = req.body.role;
        const isAdmin = role === client_1.UserRole.TENANT_ADMIN || role === client_1.UserRole.SUPER_ADMIN;
        if (!isAdmin && !course_name) {
            throw new Error("Course name is required for students and alumni");
        }
        return true;
    }),
    (0, express_validator_1.body)("batch_year")
        .optional()
        .isInt({ min: 1900, max: new Date().getFullYear() + 10 })
        .withMessage("Batch year must be a valid year")
        .custom((batch_year, { req }) => {
        const role = req.body.role;
        const isAdmin = role === client_1.UserRole.TENANT_ADMIN || role === client_1.UserRole.SUPER_ADMIN;
        if (!isAdmin && !batch_year) {
            throw new Error("Batch year is required for students and alumni");
        }
        return true;
    }),
    (0, express_validator_1.body)("role")
        .isIn([client_1.UserRole.STUDENT, client_1.UserRole.ALUMNUS, client_1.UserRole.TENANT_ADMIN, client_1.UserRole.SUPER_ADMIN])
        .withMessage("Invalid role specified"),
    exports.handleValidationErrors,
];
exports.loginValidation = [
    (0, express_validator_1.body)("email").isEmail().normalizeEmail().withMessage("Please provide a valid email address"),
    (0, express_validator_1.body)("password").notEmpty().withMessage("Password is required"),
    exports.handleValidationErrors,
];
exports.refreshTokenValidation = [
    (0, express_validator_1.body)("refreshToken").notEmpty().withMessage("Refresh token is required"),
    exports.handleValidationErrors,
];
exports.updateProfileValidation = [
    (0, express_validator_1.body)("name")
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage("Name must be between 2 and 100 characters"),
    (0, express_validator_1.body)("bio").optional().trim().isLength({ max: 500 }).withMessage("Bio must not exceed 500 characters"),
    (0, express_validator_1.body)("mobile").optional().isMobilePhone("any").withMessage("Please provide a valid mobile number"),
    (0, express_validator_1.body)("linkedinUrl").optional().isURL().withMessage("Please provide a valid LinkedIn URL"),
    (0, express_validator_1.body)("githubUrl").optional().isURL().withMessage("Please provide a valid GitHub URL"),
    (0, express_validator_1.body)("portfolioUrl").optional().isURL().withMessage("Please provide a valid portfolio URL"),
    (0, express_validator_1.body)("company").optional().trim().isLength({ max: 100 }).withMessage("Company name must not exceed 100 characters"),
    (0, express_validator_1.body)("jobTitle").optional().trim().isLength({ max: 100 }).withMessage("Job title must not exceed 100 characters"),
    (0, express_validator_1.body)("experience").optional().isInt({ min: 0, max: 50 }).withMessage("Experience must be between 0 and 50 years"),
    (0, express_validator_1.body)("location").optional().trim().isLength({ max: 100 }).withMessage("Location must not exceed 100 characters"),
    exports.handleValidationErrors,
];
exports.idValidation = [(0, express_validator_1.param)("id").isLength({ min: 1 }).withMessage("ID is required"), exports.handleValidationErrors];
exports.paginationValidation = [
    (0, express_validator_1.query)("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
    (0, express_validator_1.query)("limit").optional().isInt({ min: 1, max: 100 }).withMessage("Limit must be between 1 and 100"),
    exports.handleValidationErrors,
];
exports.connectionRequestValidation = [
    (0, express_validator_1.body)("receiverId").notEmpty().withMessage("Receiver ID is required"),
    (0, express_validator_1.body)("message").optional().trim().isLength({ max: 500 }).withMessage("Message must not exceed 500 characters"),
    exports.handleValidationErrors,
];
exports.connectionResponseValidation = [
    (0, express_validator_1.body)("status").isIn(["ACCEPTED", "REJECTED", "BLOCKED"]).withMessage("Status must be ACCEPTED, REJECTED, or BLOCKED"),
    exports.handleValidationErrors,
];
exports.createPostValidation = [
    (0, express_validator_1.body)("title").trim().isLength({ min: 5, max: 200 }).withMessage("Title must be between 5 and 200 characters"),
    (0, express_validator_1.body)("content").trim().isLength({ min: 10, max: 5000 }).withMessage("Content must be between 10 and 5000 characters"),
    (0, express_validator_1.body)("type").isIn(["ADVICE", "GENERAL", "ANNOUNCEMENT"]).withMessage("Type must be ADVICE, GENERAL, or ANNOUNCEMENT"),
    (0, express_validator_1.body)("isPublic").isBoolean().withMessage("isPublic must be a boolean"),
    (0, express_validator_1.body)("imageUrl").optional().isURL().withMessage("Please provide a valid image URL"),
    exports.handleValidationErrors,
];
exports.sendMessageValidation = [
    (0, express_validator_1.body)("receiverId").notEmpty().withMessage("Receiver ID is required"),
    (0, express_validator_1.body)("content")
        .trim()
        .isLength({ min: 1, max: 2000 })
        .withMessage("Message content must be between 1 and 2000 characters"),
    exports.handleValidationErrors,
];
exports.notificationPreferencesValidation = [
    (0, express_validator_1.body)("emailMessageReceived").optional().isBoolean().withMessage("emailMessageReceived must be a boolean"),
    (0, express_validator_1.body)("emailConnectionRequest").optional().isBoolean().withMessage("emailConnectionRequest must be a boolean"),
    (0, express_validator_1.body)("emailPostCreated").optional().isBoolean().withMessage("emailPostCreated must be a boolean"),
    (0, express_validator_1.body)("emailSystemUpdates").optional().isBoolean().withMessage("emailSystemUpdates must be a boolean"),
    (0, express_validator_1.body)("inAppMessageReceived").optional().isBoolean().withMessage("inAppMessageReceived must be a boolean"),
    (0, express_validator_1.body)("inAppConnectionRequest").optional().isBoolean().withMessage("inAppConnectionRequest must be a boolean"),
    (0, express_validator_1.body)("inAppPostCreated").optional().isBoolean().withMessage("inAppPostCreated must be a boolean"),
    (0, express_validator_1.body)("inAppSystemUpdates").optional().isBoolean().withMessage("inAppSystemUpdates must be a boolean"),
    (0, express_validator_1.body)("emailDigest").optional().isBoolean().withMessage("emailDigest must be a boolean"),
    (0, express_validator_1.body)("emailDigestFrequency")
        .optional()
        .isIn(["DAILY", "WEEKLY", "MONTHLY"])
        .withMessage("emailDigestFrequency must be DAILY, WEEKLY, or MONTHLY"),
    exports.handleValidationErrors,
];
//# sourceMappingURL=validation.js.map