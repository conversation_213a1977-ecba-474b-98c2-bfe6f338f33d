"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prisma = exports.getConnectionPoolStatus = exports.gracefulDatabaseShutdown = exports.getWriteClient = exports.getReadClient = exports.withQueryPerformanceMonitoring = exports.getConnectionInfo = exports.getDatabaseMetrics = exports.checkReadReplicaHealth = exports.checkDatabaseHealth = void 0;
const client_1 = require("@prisma/client");
const loggerService_1 = require("../services/loggerService");
const getDatabaseConfig = () => {
    const isProduction = process.env.NODE_ENV === "production";
    return {
        log: isProduction
            ? [
                { level: "error", emit: "event" },
                { level: "warn", emit: "event" },
            ]
            : [
                { level: "query", emit: "event" },
                { level: "error", emit: "event" },
                { level: "warn", emit: "event" },
                { level: "info", emit: "event" },
            ],
        errorFormat: isProduction ? "minimal" : "pretty",
    };
};
const getReadReplicaConfig = () => {
    const config = getDatabaseConfig();
    const databaseUrl = process.env.DATABASE_READ_REPLICA_URL || process.env.DATABASE_URL;
    if (!databaseUrl) {
        throw new Error("DATABASE_URL or DATABASE_READ_REPLICA_URL must be set in your environment");
    }
    return {
        ...config,
    };
};
const createPrismaClient = (config) => {
    const client = new client_1.PrismaClient(config);
    return client;
};
const prisma = globalThis.__prisma || createPrismaClient(getDatabaseConfig());
exports.prisma = prisma;
const prismaReadReplica = globalThis.__prismaReadReplica ||
    (process.env.DATABASE_READ_REPLICA_URL ? createPrismaClient(getReadReplicaConfig()) : prisma);
if (process.env.NODE_ENV === "development") {
    globalThis.__prisma = prisma;
    globalThis.__prismaReadReplica = prismaReadReplica;
}
const checkDatabaseHealth = async () => {
    try {
        await prisma.$queryRaw `SELECT 1`;
        return true;
    }
    catch (error) {
        loggerService_1.Logger.error("Database health check failed", error);
        return false;
    }
};
exports.checkDatabaseHealth = checkDatabaseHealth;
const checkReadReplicaHealth = async () => {
    if (prismaReadReplica === prisma) {
        return true;
    }
    try {
        await prismaReadReplica.$queryRaw `SELECT 1`;
        return true;
    }
    catch (error) {
        loggerService_1.Logger.error("Read replica health check failed", error);
        return false;
    }
};
exports.checkReadReplicaHealth = checkReadReplicaHealth;
const getDatabaseMetrics = async () => {
    const startTime = Date.now();
    try {
        const [userCount, postCount, connectionInfo] = await Promise.all([
            prisma.user.count(),
            prisma.generalPost.count(),
            (0, exports.getConnectionInfo)(),
        ]);
        const queryTime = Date.now() - startTime;
        loggerService_1.Logger.performance("Database metrics collection", queryTime);
        return {
            users: userCount,
            posts: postCount,
            queryTime: `${queryTime}ms`,
            connection: connectionInfo,
            timestamp: new Date().toISOString(),
        };
    }
    catch (error) {
        loggerService_1.Logger.error("Failed to collect database metrics", error);
        return null;
    }
};
exports.getDatabaseMetrics = getDatabaseMetrics;
const getConnectionInfo = async () => {
    try {
        const startTime = Date.now();
        await prisma.$queryRaw `SELECT 1`;
        const pingTime = Date.now() - startTime;
        return {
            status: "connected",
            pingTime: `${pingTime}ms`,
            hasReadReplica: prismaReadReplica !== prisma,
            timestamp: new Date().toISOString(),
        };
    }
    catch (error) {
        loggerService_1.Logger.error("Failed to get connection info", error);
        return {
            status: "error",
            error: error instanceof Error ? error.message : String(error),
            timestamp: new Date().toISOString(),
        };
    }
};
exports.getConnectionInfo = getConnectionInfo;
const withQueryPerformanceMonitoring = async (operation, queryFn) => {
    const startTime = Date.now();
    try {
        const result = await queryFn();
        const duration = Date.now() - startTime;
        loggerService_1.Logger.performance(`Database operation: ${operation}`, duration);
        if (duration > 1000) {
            loggerService_1.Logger.warn(`Slow database query detected: ${operation}`, {
                duration: `${duration}ms`,
            });
        }
        return result;
    }
    catch (error) {
        const duration = Date.now() - startTime;
        loggerService_1.Logger.error(`Database operation failed: ${operation}`, {
            error: error instanceof Error ? error.message : String(error),
            duration: `${duration}ms`,
        });
        throw error;
    }
};
exports.withQueryPerformanceMonitoring = withQueryPerformanceMonitoring;
const getReadClient = () => prismaReadReplica;
exports.getReadClient = getReadClient;
const getWriteClient = () => prisma;
exports.getWriteClient = getWriteClient;
const gracefulDatabaseShutdown = async () => {
    loggerService_1.Logger.info("Shutting down database connections...");
    try {
        await Promise.all([
            prisma.$disconnect(),
            prismaReadReplica !== prisma ? prismaReadReplica.$disconnect() : Promise.resolve(),
        ]);
        loggerService_1.Logger.info("Database connections closed successfully");
    }
    catch (error) {
        loggerService_1.Logger.error("Error during database shutdown", error);
        throw error;
    }
};
exports.gracefulDatabaseShutdown = gracefulDatabaseShutdown;
const getConnectionPoolStatus = () => {
    return {
        active: "unknown",
        idle: "unknown",
        total: "unknown",
        waiting: "unknown",
        note: "Prisma manages connection pooling internally. For detailed metrics, consider using a custom connection pool or database-level monitoring.",
    };
};
exports.getConnectionPoolStatus = getConnectionPoolStatus;
const gracefulShutdown = async (signal) => {
    console.log(`Received ${signal}, starting graceful shutdown...`);
    try {
        await prisma.$disconnect();
        console.log("Database connections closed successfully");
    }
    catch (error) {
        console.error("Error during database shutdown:", error);
    }
    process.exit(0);
};
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));
process.on("beforeExit", async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=database.js.map