{"version": 3, "file": "socketUtils.js", "sourceRoot": "", "sources": ["../../src/utils/socketUtils.ts"], "names": [], "mappings": ";;;AAAA,6DAA0D;AAC1D,yEAAsE;AACtE,6CAA+C;AAC/C,6CAA+D;AAC/D,4CAMyB;AACzB,6DAAmD;AAInD,MAAa,WAAW;IAEtB,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,UAAkB,EAAE,WAAmB,EAAE,QAAgB;QACzF,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,6BAA6B,CAAC,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC7F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,UAAkB,EAAE,WAAmB,EAAE,QAAgB;QAC1F,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,8BAA8B,CAAC,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC9F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,WAAmB,EAAE,QAAgB,EAAE,QAAgB;QAC3F,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,iCAAiC,CAAC,WAAW,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC/F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,WAAmB,EAAE,UAAkB,EAAE,QAAgB;QAC9F,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,kCAAkC,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAClG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,QAAgB,EAAE,UAAkB,EAAE,QAAgB,EAAE,OAAe;QAC7G,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,uBAAuB,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC7F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAe,EAAE,QAAgB,EAAE,QAAgB;QAClF,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,qBAAqB,CAAC,OAAO,EAAE,QAAQ,EAAE,wBAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACxG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAe,EAAE,QAAgB,EAAE,QAAgB;QAClF,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,qBAAqB,CAAC,OAAO,EAAE,QAAQ,EAAE,wBAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACxG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAe,EAAE,QAAgB,EAAE,QAAgB;QAClF,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,qBAAqB,CAAC,OAAO,EAAE,QAAQ,EAAE,wBAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACxG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,QAAgB,EAAE,QAAgB;QAC7E,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,sBAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACjG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAa,EAAE,QAAgB,EAAE,QAAgB;QAC9E,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,sBAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAClG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAa,EAAE,QAAgB,EAAE,QAAgB;QAC9E,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,sBAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAClG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,QAAgB,EAAE,OAAe,EAAE,YAAoB;QACnG,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE,uBAAc,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAChH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,QAAgB,EAAE,WAAmB,EAAE,YAAoB;QAC3G,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE,uBAAc,CAAC,SAAS,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;QACxH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,QAAgB,EAAE,KAAa,EAAE,OAAe,EAAE,IAAU;QACjG,IAAI,CAAC;YACH,MAAM,yCAAmB,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACnF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,cAAc,CAAC,QAAgB;QACpC,IAAI,CAAC;YACH,OAAO,6BAAa,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,YAAY,CAAC,MAAc;QAChC,IAAI,CAAC;YACH,OAAO,6BAAa,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,sBAAsB,CACjC,MAAc,EACd,QAAgB,EAChB,IAAsB,EACtB,KAAa,EACb,OAAe,EACf,IAAU;QAEV,IAAI,CAAC;YACH,MAAM,YAAY,GAAuB;gBACvC,MAAM;gBACN,QAAQ;gBACR,IAAI;gBACJ,KAAK;gBACL,OAAO;gBACP,IAAI;gBACJ,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,6BAAa,CAAC,sBAAsB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,6BAA6B,CACxC,OAAiB,EACjB,QAAgB,EAChB,IAAsB,EACtB,KAAa,EACb,OAAe,EACf,IAAU;QAEV,IAAI,CAAC;YACH,MAAM,YAAY,GAAuB;gBACvC,MAAM,EAAE,CAAC;gBACT,QAAQ;gBACR,IAAI;gBACJ,KAAK;gBACL,OAAO;gBACP,IAAI;gBACJ,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,6BAAa,CAAC,uBAAuB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,KAAa,EAAE,IAAS;QACvE,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,IAAA,oBAAW,GAAE,CAAC;YACzB,EAAE,CAAC,EAAE,CAAC,qBAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACvD,sBAAM,CAAC,IAAI,CAAC,eAAe,KAAK,cAAc,QAAQ,EAAE,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAiB,EAAE,KAAa,EAAE,IAAS;QACvE,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,IAAA,oBAAW,GAAE,CAAC;YACzB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACvB,EAAE,CAAC,EAAE,CAAC,qBAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YACH,sBAAM,CAAC,IAAI,CAAC,eAAe,KAAK,OAAO,OAAO,CAAC,MAAM,QAAQ,CAAC,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE,YAAoB,EAAE,MAAc,EAAE,IAAU;QAChG,IAAI,CAAC;YACH,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,YAAY;gBAClB,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,IAAI;aACL,CAAC;YAEF,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC;QAC1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,MAAc;QACxC,IAAI,CAAC;YACH,OAAO,MAAM,6BAAa,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,MAAe;QAC9D,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,IAAA,oBAAW,GAAE,CAAC;YACzB,MAAM,SAAS,GAAG,6BAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAEzD,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAChD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,CAAC,IAAI,CAAC,sBAAa,CAAC,KAAK,EAAE;wBAC/B,IAAI,EAAE,kBAAkB;wBACxB,OAAO,EAAE,MAAM,IAAI,gDAAgD;qBACpE,CAAC,CAAC;oBACH,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC1B,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,sBAAM,CAAC,IAAI,CAAC,2BAA2B,MAAM,aAAa,MAAM,IAAI,cAAc,EAAE,CAAC,CAAC;QACxF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAGD,MAAM,CAAC,cAAc;QACnB,IAAI,CAAC;YACH,MAAM,EAAE,GAAG,IAAA,oBAAW,GAAE,CAAC;YACzB,MAAM,WAAW,GAAG,6BAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;YAE7D,OAAO;gBACL,gBAAgB,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY;gBACxC,gBAAgB,EAAE,WAAW,CAAC,MAAM;gBACpC,YAAY,EAAE,OAAO,CAAC,MAAM,EAAE;gBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sBAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO;gBACL,gBAAgB,EAAE,CAAC;gBACnB,gBAAgB,EAAE,CAAC;gBACnB,YAAY,EAAE,CAAC;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAjSD,kCAiSC;AAED,kBAAe,WAAW,CAAC"}