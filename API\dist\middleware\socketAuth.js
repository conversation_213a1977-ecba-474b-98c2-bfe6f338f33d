"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.socketEventLogger = exports.emitValidationError = exports.emitSocketError = exports.socketRateLimit = exports.validateEventData = exports.requireSameTenant = exports.requireRole = exports.socketAuthMiddleware = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const socket_1 = require("../config/socket");
const loggerService_1 = require("../services/loggerService");
const prisma = new client_1.PrismaClient();
const socketAuthMiddleware = async (socket, next) => {
    try {
        const token = socket.handshake.auth?.token || socket.handshake.headers?.authorization?.replace("Bearer ", "");
        if (!token) {
            const error = new Error("Authentication token required");
            error.data = { code: "NO_TOKEN", message: "Authentication token is required" };
            return next(error);
        }
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        if (!decoded.userId || !decoded.tenantId) {
            const error = new Error("Invalid token payload");
            error.data = { code: "INVALID_TOKEN", message: "Token payload is invalid" };
            return next(error);
        }
        const user = await prisma.user.findFirst({
            where: {
                id: decoded.userId,
                tenant_id: decoded.tenantId,
                account_status: "APPROVED",
            },
            include: {
                tenant: {
                    select: {
                        id: true,
                        name: true,
                        is_active: true,
                    },
                },
            },
        });
        if (!user) {
            const error = new Error("User not found or not approved");
            error.data = { code: "USER_NOT_FOUND", message: "User not found or account not approved" };
            return next(error);
        }
        if (!user.tenant.is_active) {
            const error = new Error("Tenant is not active");
            error.data = { code: "TENANT_INACTIVE", message: "Tenant account is not active" };
            return next(error);
        }
        socket.userId = user.id;
        socket.tenantId = user.tenant_id;
        socket.userRole = user.role;
        socket.isAuthenticated = true;
        loggerService_1.Logger.info(`Socket authenticated for user ${user.id} (${user.full_name}) in tenant ${user.tenant_id}`);
        next();
    }
    catch (error) {
        loggerService_1.Logger.error("Socket authentication error:", error);
        let errorMessage = "Authentication failed";
        let errorCode = "AUTH_FAILED";
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            errorMessage = "Invalid token";
            errorCode = "INVALID_TOKEN";
        }
        else if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            errorMessage = "Token expired";
            errorCode = "TOKEN_EXPIRED";
        }
        const authError = new Error(errorMessage);
        authError.data = { code: errorCode, message: errorMessage };
        next(authError);
    }
};
exports.socketAuthMiddleware = socketAuthMiddleware;
const requireRole = (allowedRoles) => {
    return (socket, next) => {
        if (!socket.isAuthenticated || !socket.userRole) {
            const error = new Error("Not authenticated");
            error.data = { code: "NOT_AUTHENTICATED", message: "Socket is not authenticated" };
            return next(error);
        }
        if (!allowedRoles.includes(socket.userRole)) {
            const error = new Error("Insufficient permissions");
            error.data = { code: "INSUFFICIENT_PERMISSIONS", message: "User role not authorized for this action" };
            return next(error);
        }
        next();
    };
};
exports.requireRole = requireRole;
const requireSameTenant = (targetTenantId) => {
    return (socket, next) => {
        if (!socket.isAuthenticated || !socket.tenantId) {
            const error = new Error("Not authenticated");
            error.data = { code: "NOT_AUTHENTICATED", message: "Socket is not authenticated" };
            return next(error);
        }
        if (socket.tenantId !== targetTenantId) {
            const error = new Error("Tenant access denied");
            error.data = { code: "TENANT_ACCESS_DENIED", message: "User does not belong to the target tenant" };
            return next(error);
        }
        next();
    };
};
exports.requireSameTenant = requireSameTenant;
const validateEventData = (validator) => {
    return (data, socket, next) => {
        if (!validator(data)) {
            const error = new Error("Invalid event data");
            error.data = { code: "INVALID_DATA", message: "Event data validation failed" };
            return next(error);
        }
        next();
    };
};
exports.validateEventData = validateEventData;
const eventCounts = new Map();
const socketRateLimit = (maxEvents = 100, windowMs = 60000) => {
    return (socket, next) => {
        const key = `${socket.userId}_${socket.tenantId}`;
        const now = Date.now();
        const userEvents = eventCounts.get(key);
        if (!userEvents || now > userEvents.resetTime) {
            eventCounts.set(key, { count: 1, resetTime: now + windowMs });
            return next();
        }
        if (userEvents.count >= maxEvents) {
            const error = new Error("Rate limit exceeded");
            error.data = { code: "RATE_LIMIT_EXCEEDED", message: "Too many events sent" };
            return next(error);
        }
        userEvents.count++;
        next();
    };
};
exports.socketRateLimit = socketRateLimit;
setInterval(() => {
    const now = Date.now();
    for (const [key, data] of eventCounts.entries()) {
        if (now > data.resetTime) {
            eventCounts.delete(key);
        }
    }
}, 60000);
const emitSocketError = (socket, error) => {
    socket.emit(socket_1.SOCKET_EVENTS.ERROR, error);
    loggerService_1.Logger.warn(`Socket error emitted to ${socket.id}:`, error);
};
exports.emitSocketError = emitSocketError;
const emitValidationError = (socket, message, details) => {
    const error = {
        code: "VALIDATION_ERROR",
        message,
        details,
    };
    socket.emit(socket_1.SOCKET_EVENTS.VALIDATION_ERROR, error);
    loggerService_1.Logger.warn(`Validation error emitted to ${socket.id}:`, error);
};
exports.emitValidationError = emitValidationError;
const socketEventLogger = (eventName) => {
    return (socket, data, next) => {
        loggerService_1.Logger.info(`Socket event ${eventName} from user ${socket.userId} (${socket.id})`, {
            userId: socket.userId,
            tenantId: socket.tenantId,
            eventName,
            socketId: socket.id,
            dataKeys: data ? Object.keys(data) : [],
        });
        next();
    };
};
exports.socketEventLogger = socketEventLogger;
//# sourceMappingURL=socketAuth.js.map