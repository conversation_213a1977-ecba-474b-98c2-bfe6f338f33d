import { Request, Response, NextFunction } from "express";
declare const API_VERSIONS: {
    readonly v1: "1.0.0";
    readonly v2: "2.0.0";
};
type ApiVersion = keyof typeof API_VERSIONS;
declare global {
    namespace Express {
        interface Request {
            apiVersion?: ApiVersion;
            requestedVersion?: string | undefined;
        }
    }
}
export declare const apiVersioning: () => (req: Request, res: Response, next: NextFunction) => void | Response<any, Record<string, any>>;
export declare const versionedRoute: (handlers: Partial<Record<ApiVersion, any>>) => (req: Request, res: Response, next: NextFunction) => any;
export declare const deprecationWarning: (version: ApiVersion, deprecatedIn: string, removedIn: string, message?: string) => (req: Request, res: Response, next: NextFunction) => void;
export declare const versionMigration: (fromVersion: ApiVersion, toVersion: ApiVersion, migrationFn: (data: any) => any) => (req: Request, res: Response, next: NextFunction) => void;
export declare const contentNegotiation: () => (req: Request, res: Response, next: NextFunction) => void;
export declare const versionAnalytics: () => (req: Request, _res: Response, next: NextFunction) => void;
export declare const getVersionInfo: () => {
    versions: {
        readonly v1: "1.0.0";
        readonly v2: "2.0.0";
    };
    defaultVersion: "v1";
    compatibility: {
        readonly v1: readonly ["v1"];
        readonly v2: readonly ["v2", "v1"];
    };
    supportedVersions: string[];
};
export {};
//# sourceMappingURL=versioning.d.ts.map