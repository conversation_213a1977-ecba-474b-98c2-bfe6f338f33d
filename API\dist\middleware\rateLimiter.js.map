{"version": 3, "file": "rateLimiter.js", "sourceRoot": "", "sources": ["../../src/middleware/rateLimiter.ts"], "names": [], "mappings": ";;;;;;AAAA,4EAA2C;AAC3C,2CAAiD;AACjD,6DAAmD;AAGnD,MAAM,cAAc;IAApB;QACU,UAAK,GAAG,sBAAc,CAAC;IA+BjC,CAAC;IA7BC,KAAK,CAAC,SAAS,CAAC,GAAW;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAS,GAAG,CAAC,IAAI,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,OAAO,GAAG,CAAC,CAAC;QAG9B,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;YAClB,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,QAAQ,CAAC,CAAC;YACxE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC;YAClD,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACnC,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;YACtE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,CAAC;QAChD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,GAAW;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAS,GAAG,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,GAAW;QACxB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;CACF;AAGD,MAAM,WAAW,GAAG,IAAI,cAAc,EAAE,CAAC;AAG5B,QAAA,WAAW,GAAG,IAAA,4BAAS,EAAC;IACnC,KAAK,EAAE,WAAW;IAClB,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,QAAQ,CAAC;IAChE,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,KAAK,CAAC;IAC3D,OAAO,EAAE;QACP,KAAK,EAAE,yDAAyD;QAChE,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,QAAQ,CAAC,GAAG,IAAI,CAAC;KACrF;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACpB,sBAAM,CAAC,QAAQ,CAAC,qBAAqB,EAAE,GAAG,CAAC,EAAE,EAAE;YAC7C,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,QAAQ,EAAE,GAAG,CAAC,IAAI;YAClB,MAAM,EAAE,GAAG,CAAC,MAAM;SACnB,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,yDAAyD;YAChE,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,QAAQ,CAAC,GAAG,IAAI,CAAC;SACrF,CAAC,CAAC;IACL,CAAC;IACD,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE;QAEZ,OAAO,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC;IAChC,CAAC;CACF,CAAC,CAAC;AAGU,QAAA,eAAe,GAAG,IAAA,4BAAS,EAAC;IACvC,KAAK,EAAE,IAAI,cAAc,EAAE;IAC3B,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,GAAG,EAAE,CAAC;IACN,OAAO,EAAE;QACP,KAAK,EAAE,2DAA2D;QAClE,UAAU,EAAE,GAAG;KAChB;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;IACpB,OAAO,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;QACrB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,2DAA2D;YAClE,UAAU,EAAE,GAAG;SAChB,CAAC,CAAC;IACL,CAAC;CACF,CAAC,CAAC;AAGH,sBAAM,CAAC,IAAI,CAAC,uDAAuD,EAAE;IACnE,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,QAAQ,CAAC;IACvE,kBAAkB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,KAAK,CAAC;IAC1E,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAC5B,eAAe,EAAE,CAAC;CACnB,CAAC,CAAC"}