const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

const baseURL = 'http://localhost:5000';

async function testFileValidation() {
  console.log('🔍 Testing File Type Validation...\n');

  try {
    // Login first
    const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'AdminPassword123!'
    });
    const token = loginResponse.data.accessToken;
    console.log('✅ Logged in successfully');

    // Test 1: Valid PNG file
    console.log('\n1. Testing valid PNG file...');
    const validPNG = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==', 'base64');
    fs.writeFileSync('test-valid.png', validPNG);
    
    const form1 = new FormData();
    form1.append('profilePicture', fs.createReadStream('test-valid.png'));
    
    const response1 = await axios.post(`${baseURL}/api/users/profile/picture`, form1, {
      headers: { ...form1.getHeaders(), 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Valid PNG accepted:', response1.data.message);

    // Test 2: Invalid text file
    console.log('\n2. Testing invalid text file...');
    fs.writeFileSync('test-invalid.txt', 'This is not an image');
    
    const form2 = new FormData();
    form2.append('profilePicture', fs.createReadStream('test-invalid.txt'));
    
    try {
      await axios.post(`${baseURL}/api/users/profile/picture`, form2, {
        headers: { ...form2.getHeaders(), 'Authorization': `Bearer ${token}` }
      });
      console.log('❌ Text file should have been rejected');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Text file properly rejected:', error.response.data.error || error.response.data.message);
      }
    }

    // Test 3: Valid JPEG file
    console.log('\n3. Testing valid JPEG file...');
    // Minimal JPEG header
    const validJPEG = Buffer.from('/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A', 'base64');
    fs.writeFileSync('test-valid.jpg', validJPEG);
    
    const form3 = new FormData();
    form3.append('profilePicture', fs.createReadStream('test-valid.jpg'));
    
    const response3 = await axios.post(`${baseURL}/api/users/profile/picture`, form3, {
      headers: { ...form3.getHeaders(), 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Valid JPEG accepted:', response3.data.message);

    // Cleanup
    fs.unlinkSync('test-valid.png');
    fs.unlinkSync('test-invalid.txt');
    fs.unlinkSync('test-valid.jpg');
    console.log('\n✅ File validation test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFileValidation();
