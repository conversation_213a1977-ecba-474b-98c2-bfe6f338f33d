"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const socketUtils_1 = require("../utils/socketUtils");
const loggerService_1 = require("../services/loggerService");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get("/online-users", auth_1.requireApproved, async (req, res) => {
    try {
        const tenantId = req.user?.tenant_id;
        if (!tenantId) {
            return res.status(400).json({ error: "Tenant ID not found" });
        }
        const onlineUsers = socketUtils_1.SocketUtils.getOnlineUsers(tenantId);
        return res.json({
            success: true,
            data: onlineUsers,
            count: onlineUsers.length,
        });
    }
    catch (error) {
        loggerService_1.Logger.error("Error getting online users:", error);
        return res.status(500).json({ error: "Failed to get online users" });
    }
});
router.get("/user/:userId/online", auth_1.requireApproved, async (req, res) => {
    try {
        const userId = parseInt(req.params.userId);
        if (isNaN(userId)) {
            return res.status(400).json({ error: "Invalid user ID" });
        }
        const isOnline = socketUtils_1.SocketUtils.isUserOnline(userId);
        return res.json({
            success: true,
            data: { userId, isOnline },
        });
    }
    catch (error) {
        loggerService_1.Logger.error("Error checking user online status:", error);
        return res.status(500).json({ error: "Failed to check user online status" });
    }
});
router.post("/test-notification", auth_1.requireApproved, async (req, res) => {
    try {
        const userId = req.user?.id;
        const tenantId = req.user?.tenant_id;
        const { title, message, type = "SYSTEM" } = req.body;
        if (!userId || !tenantId) {
            return res.status(400).json({ error: "User or tenant ID not found" });
        }
        if (!title || !message) {
            return res.status(400).json({ error: "Title and message are required" });
        }
        await socketUtils_1.SocketUtils.sendCustomNotification(parseInt(userId), tenantId, type, title, message, {
            test: true,
            timestamp: new Date(),
        });
        return res.json({
            success: true,
            message: "Test notification sent",
        });
    }
    catch (error) {
        loggerService_1.Logger.error("Error sending test notification:", error);
        return res.status(500).json({ error: "Failed to send test notification" });
    }
});
router.post("/broadcast-notification", auth_1.requireApproved, async (req, res) => {
    try {
        const currentUser = req.user;
        const { userIds, title, message, type = "SYSTEM" } = req.body;
        if (!currentUser || !["TENANT_ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
            return res.status(403).json({ error: "Insufficient permissions" });
        }
        if (!Array.isArray(userIds) || userIds.length === 0) {
            return res.status(400).json({ error: "User IDs array is required" });
        }
        if (!title || !message) {
            return res.status(400).json({ error: "Title and message are required" });
        }
        await socketUtils_1.SocketUtils.sendCustomNotificationToUsers(userIds, currentUser.tenant_id, type, title, message, { broadcastBy: currentUser.userId, timestamp: new Date() });
        return res.json({
            success: true,
            message: `Notification sent to ${userIds.length} users`,
        });
    }
    catch (error) {
        loggerService_1.Logger.error("Error broadcasting notification:", error);
        return res.status(500).json({ error: "Failed to broadcast notification" });
    }
});
router.post("/system-notification", auth_1.requireApproved, async (req, res) => {
    try {
        const currentUser = req.user;
        const { title, message } = req.body;
        if (!currentUser || !["TENANT_ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
            return res.status(403).json({ error: "Insufficient permissions" });
        }
        if (!title || !message) {
            return res.status(400).json({ error: "Title and message are required" });
        }
        await socketUtils_1.SocketUtils.triggerSystemNotification(currentUser.tenant_id, title, message, {
            sentBy: currentUser.userId,
            timestamp: new Date(),
        });
        return res.json({
            success: true,
            message: "System notification sent to all users in tenant",
        });
    }
    catch (error) {
        loggerService_1.Logger.error("Error sending system notification:", error);
        return res.status(500).json({ error: "Failed to send system notification" });
    }
});
router.get("/stats", auth_1.requireApproved, async (req, res) => {
    try {
        const currentUser = req.user;
        if (!currentUser || !["TENANT_ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
            return res.status(403).json({ error: "Insufficient permissions" });
        }
        const stats = socketUtils_1.SocketUtils.getServerStats();
        return res.json({
            success: true,
            data: stats,
        });
    }
    catch (error) {
        loggerService_1.Logger.error("Error getting server stats:", error);
        return res.status(500).json({ error: "Failed to get server statistics" });
    }
});
router.post("/disconnect-user", auth_1.requireApproved, async (req, res) => {
    try {
        const currentUser = req.user;
        const { userId, reason } = req.body;
        if (!currentUser || !["TENANT_ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
            return res.status(403).json({ error: "Insufficient permissions" });
        }
        if (!userId) {
            return res.status(400).json({ error: "User ID is required" });
        }
        await socketUtils_1.SocketUtils.forceDisconnectUser(userId, reason || `Disconnected by ${currentUser.userId}`);
        return res.json({
            success: true,
            message: `User ${userId} has been disconnected`,
        });
    }
    catch (error) {
        loggerService_1.Logger.error("Error disconnecting user:", error);
        return res.status(500).json({ error: "Failed to disconnect user" });
    }
});
router.get("/room/:roomId/members", auth_1.requireApproved, async (req, res) => {
    try {
        const currentUser = req.user;
        const { roomId } = req.params;
        if (!currentUser || !["TENANT_ADMIN", "SUPER_ADMIN"].includes(currentUser.role)) {
            return res.status(403).json({ error: "Insufficient permissions" });
        }
        const members = await socketUtils_1.SocketUtils.getRoomMembers(roomId);
        return res.json({
            success: true,
            data: { roomId, members, count: members.length },
        });
    }
    catch (error) {
        loggerService_1.Logger.error("Error getting room members:", error);
        return res.status(500).json({ error: "Failed to get room members" });
    }
});
router.post("/activity", auth_1.requireApproved, async (req, res) => {
    try {
        const currentUser = req.user;
        const { activityType, data } = req.body;
        if (!currentUser) {
            return res.status(400).json({ error: "User not found" });
        }
        if (!activityType) {
            return res.status(400).json({ error: "Activity type is required" });
        }
        await socketUtils_1.SocketUtils.sendActivityUpdate(currentUser.tenant_id, activityType, parseInt(currentUser.id), data);
        return res.json({
            success: true,
            message: "Activity update sent",
        });
    }
    catch (error) {
        loggerService_1.Logger.error("Error sending activity update:", error);
        return res.status(500).json({ error: "Failed to send activity update" });
    }
});
exports.default = router;
//# sourceMappingURL=socket.js.map