const axios = require("axios");
const fs = require("fs");
const path = require("path");
const FormData = require("form-data");

const baseURL = "http://localhost:5000";

// Create a simple test image
const createTestImage = () => {
  const base64PNG = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==";
  const buffer = Buffer.from(base64PNG, "base64");
  const testImagePath = path.join(__dirname, "test.png");
  fs.writeFileSync(testImagePath, buffer);
  return testImagePath;
};

async function testImageUploadSystem() {
  console.log("🧪 Testing Image Upload System...\n");

  try {
    // Test 1: Server Health
    console.log("1. Testing server health...");
    const health = await axios.get(`${baseURL}/health`);
    console.log("✅ Server is healthy:", health.data.status);

    // Test 2: Register User
    console.log("\n2. Registering test user...");
    const userData = {
      email: `test-${Date.now()}@example.com`,
      password: "TestPassword123!",
      full_name: "Test User",
      usn: "CS2024001", // Valid USN format: 2-4 letters + 4 digits + 3 digits
      course_name: "Computer Science",
      batch_year: 2024,
      role: "STUDENT",
      tenant_id: 1,
    };

    const registerResponse = await axios.post(`${baseURL}/api/auth/register`, userData);
    console.log("✅ User registered:", registerResponse.data.message);

    // Test 3: Login
    console.log("\n3. Logging in...");
    const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
      email: userData.email,
      password: userData.password,
    });
    console.log("✅ Login successful");
    const token = loginResponse.data.accessToken;

    // Test 4: Create test image
    console.log("\n4. Creating test image...");
    const imagePath = createTestImage();
    console.log("✅ Test image created");

    // Test 5: Upload profile picture
    console.log("\n5. Uploading profile picture...");
    const form = new FormData();
    form.append("profilePicture", fs.createReadStream(imagePath));

    const uploadResponse = await axios.post(`${baseURL}/api/users/profile/picture`, form, {
      headers: {
        ...form.getHeaders(),
        Authorization: `Bearer ${token}`,
      },
    });

    console.log("✅ Profile picture uploaded successfully!");
    console.log("   Message:", uploadResponse.data.message);
    console.log("   Image URL:", uploadResponse.data.profilePicture.url);

    // Test 6: Verify image is accessible
    console.log("\n6. Testing image accessibility...");
    const imageUrl = uploadResponse.data.profilePicture.url;
    const imageResponse = await axios.get(`${baseURL}${imageUrl}`);
    console.log("✅ Image is accessible");
    console.log("   Content-Type:", imageResponse.headers["content-type"]);
    console.log("   Size:", imageResponse.headers["content-length"], "bytes");

    // Test 7: Upload replacement image
    console.log("\n7. Testing image replacement...");
    const form2 = new FormData();
    form2.append("profilePicture", fs.createReadStream(imagePath));

    const uploadResponse2 = await axios.post(`${baseURL}/api/users/profile/picture`, form2, {
      headers: {
        ...form2.getHeaders(),
        Authorization: `Bearer ${token}`,
      },
    });

    console.log("✅ Image replaced successfully!");
    console.log("   New URL:", uploadResponse2.data.profilePicture.url);

    // Cleanup
    console.log("\n8. Cleaning up...");
    fs.unlinkSync(imagePath);
    console.log("✅ Test files cleaned up");

    console.log("\n🎉 All tests passed! Image upload system is working correctly.");

    console.log("\n📋 Test Summary:");
    console.log("✅ Server health check");
    console.log("✅ User registration");
    console.log("✅ User authentication");
    console.log("✅ Image upload");
    console.log("✅ Static file serving");
    console.log("✅ Image replacement");
    console.log("✅ File cleanup");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    if (error.response) {
      console.error("Status:", error.response.status);
      console.error("Data:", error.response.data);
    }
  }
}

testImageUploadSystem();
