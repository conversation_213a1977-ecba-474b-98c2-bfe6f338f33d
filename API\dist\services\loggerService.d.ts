import winston from "winston";
declare const logger: winston.Logger;
export declare class Logger {
    static error(message: string, meta?: any): void;
    static warn(message: string, meta?: any): void;
    static info(message: string, meta?: any): void;
    static http(message: string, meta?: any): void;
    static debug(message: string, meta?: any): void;
    static auth(message: string, userId?: string, meta?: any): void;
    static database(message: string, query?: string, meta?: any): void;
    static api(message: string, endpoint?: string, method?: string, meta?: any): void;
    static security(message: string, ip?: string, meta?: any): void;
    static performance(message: string, duration?: number, meta?: any): void;
    static queue(message: string, queueName?: string, jobId?: string, meta?: any): void;
    static cache(message: string, key?: string, meta?: any): void;
    static websocket(message: string, userId?: string, meta?: any): void;
    static requestLogger(): (req: any, res: any, next: any) => void;
    static errorLogger(): (err: any, req: any, res: any, next: any) => void;
    static measurePerformance<T>(operation: string, fn: () => Promise<T>, meta?: any): Promise<T>;
    static startup(message: string, meta?: any): void;
    static shutdown(message: string, meta?: any): void;
    static health(service: string, status: "healthy" | "unhealthy", meta?: any): void;
    static business(event: string, userId?: string, meta?: any): void;
}
export declare const morganStream: {
    write: (message: string) => void;
};
export { logger };
export default Logger;
//# sourceMappingURL=loggerService.d.ts.map