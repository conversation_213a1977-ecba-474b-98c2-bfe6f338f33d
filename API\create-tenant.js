const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTenant() {
  try {
    console.log('Creating test tenant...');
    
    // Create a tenant
    const tenant = await prisma.tenant.upsert({
      where: { subdomain: 'test' },
      update: {},
      create: {
        name: 'Test University',
        subdomain: 'test',
        logo_url: null,
        is_active: true
      }
    });

    console.log('✅ Tenant created:', tenant);

    // Create a course
    const course = await prisma.course.upsert({
      where: { id: 1 },
      update: {},
      create: {
        tenant_id: tenant.id,
        course_name: 'Computer Science'
      }
    });

    console.log('✅ Course created:', course);

    console.log('\n🎉 Setup completed! You can now test user registration.');
    console.log(`Tenant ID: ${tenant.id}`);
    console.log(`Course ID: ${course.id}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createTenant();
