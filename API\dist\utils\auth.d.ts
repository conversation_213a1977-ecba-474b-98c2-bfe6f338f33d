export interface JWTPayload {
    userId: string;
    email: string;
    role: string;
    status: string;
}
export interface TokenPair {
    accessToken: string;
    refreshToken: string;
}
export interface RefreshTokenPayload {
    userId: string;
    email: string;
    role: string;
    status: string;
}
export declare class AuthUtils {
    private static readonly JWT_SECRET;
    private static readonly JWT_REFRESH_SECRET;
    private static readonly JWT_EXPIRES_IN;
    private static readonly JWT_REFRESH_EXPIRES_IN;
    private static readonly BCRYPT_ROUNDS;
    static hashPassword(password: string): Promise<string>;
    static comparePassword(password: string, hash: string): Promise<boolean>;
    static generateAccessToken(payload: JWTPayload): string;
    static generateRefreshToken(payload: JWTPayload): string;
    static generateTokenPair(user: any): TokenPair;
    static verifyAccessToken(token: string): JWTPayload;
    static verifyRefreshToken(token: string): JWTPayload;
    static extractTokenFromHeader(authHeader: string | undefined): string | null;
    static generateRandomPassword(length?: number): string;
    static validatePassword(password: string): {
        isValid: boolean;
        errors: string[];
    };
    static validateEmail(email: string): boolean;
    static validateUSN(usn: string): boolean;
    static verifySessionRefreshToken(token: string): RefreshTokenPayload | null;
    static generateSessionAccessToken(payload: JWTPayload): string;
    static generateSessionRefreshToken(payload: RefreshTokenPayload): string;
}
//# sourceMappingURL=auth.d.ts.map