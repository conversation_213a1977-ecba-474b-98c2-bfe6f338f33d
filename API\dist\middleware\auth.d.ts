import { Request, Response, NextFunction } from "express";
import { UserRole, UserStatus } from "@prisma/client";
interface AuthenticatedRequest extends Request {
    user?: {
        userId: string;
        email: string;
        role: UserRole;
        status: UserStatus;
        id: string;
        tenant_id: number;
    };
}
export declare const authenticate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireApproved: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const authorize: (...roles: UserRole[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const requireTenantAdmin: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const requireSuperAdmin: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const requireAlumni: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const requireStudent: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const requireAlumniOrAdmin: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const requireStudentOrAlumni: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const optionalAuth: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export {};
//# sourceMappingURL=auth.d.ts.map