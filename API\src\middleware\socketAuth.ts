import jwt from "jsonwebtoken";
import { Socket } from "socket.io";
import { ExtendedError } from "socket.io/dist/namespace";
import { PrismaClient } from "@prisma/client";
import { AuthenticatedSocket, SocketAuthData, SocketError } from "../types/socket";
import { SOCKET_EVENTS } from "../config/socket";
import { Logger } from "../services/loggerService";

const prisma = new PrismaClient();

// JWT payload interface
interface JWTPayload {
  userId: number;
  tenantId: number;
  role: string;
  iat?: number;
  exp?: number;
}

// Socket authentication middleware
export const socketAuthMiddleware = async (
  socket: AuthenticatedSocket,
  next: (err?: ExtendedError) => void
) => {
  try {
    const token = socket.handshake.auth?.token || socket.handshake.headers?.authorization?.replace("Bearer ", "");

    if (!token) {
      const error: ExtendedError = new Error("Authentication token required");
      error.data = { code: "NO_TOKEN", message: "Authentication token is required" };
      return next(error);
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;

    if (!decoded.userId || !decoded.tenantId) {
      const error: ExtendedError = new Error("Invalid token payload");
      error.data = { code: "INVALID_TOKEN", message: "Token payload is invalid" };
      return next(error);
    }

    // Fetch user from database to ensure they still exist and are active
    const user = await prisma.user.findFirst({
      where: {
        id: decoded.userId,
        tenant_id: decoded.tenantId,
        account_status: "APPROVED", // Only allow approved users
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            is_active: true,
          },
        },
      },
    });

    if (!user) {
      const error: ExtendedError = new Error("User not found or not approved");
      error.data = { code: "USER_NOT_FOUND", message: "User not found or account not approved" };
      return next(error);
    }

    if (!user.tenant.is_active) {
      const error: ExtendedError = new Error("Tenant is not active");
      error.data = { code: "TENANT_INACTIVE", message: "Tenant account is not active" };
      return next(error);
    }

    // Attach user information to socket
    socket.userId = user.id;
    socket.tenantId = user.tenant_id;
    socket.userRole = user.role;
    socket.isAuthenticated = true;

    Logger.info(`Socket authenticated for user ${user.id} (${user.full_name}) in tenant ${user.tenant_id}`);
    next();
  } catch (error) {
    Logger.error("Socket authentication error:", error);
    
    let errorMessage = "Authentication failed";
    let errorCode = "AUTH_FAILED";

    if (error instanceof jwt.JsonWebTokenError) {
      errorMessage = "Invalid token";
      errorCode = "INVALID_TOKEN";
    } else if (error instanceof jwt.TokenExpiredError) {
      errorMessage = "Token expired";
      errorCode = "TOKEN_EXPIRED";
    }

    const authError: ExtendedError = new Error(errorMessage);
    authError.data = { code: errorCode, message: errorMessage };
    next(authError);
  }
};

// Authorization middleware for specific actions
export const requireRole = (allowedRoles: string[]) => {
  return (socket: AuthenticatedSocket, next: (err?: ExtendedError) => void) => {
    if (!socket.isAuthenticated || !socket.userRole) {
      const error: ExtendedError = new Error("Not authenticated");
      error.data = { code: "NOT_AUTHENTICATED", message: "Socket is not authenticated" };
      return next(error);
    }

    if (!allowedRoles.includes(socket.userRole)) {
      const error: ExtendedError = new Error("Insufficient permissions");
      error.data = { code: "INSUFFICIENT_PERMISSIONS", message: "User role not authorized for this action" };
      return next(error);
    }

    next();
  };
};

// Middleware to ensure user belongs to the same tenant
export const requireSameTenant = (targetTenantId: number) => {
  return (socket: AuthenticatedSocket, next: (err?: ExtendedError) => void) => {
    if (!socket.isAuthenticated || !socket.tenantId) {
      const error: ExtendedError = new Error("Not authenticated");
      error.data = { code: "NOT_AUTHENTICATED", message: "Socket is not authenticated" };
      return next(error);
    }

    if (socket.tenantId !== targetTenantId) {
      const error: ExtendedError = new Error("Tenant access denied");
      error.data = { code: "TENANT_ACCESS_DENIED", message: "User does not belong to the target tenant" };
      return next(error);
    }

    next();
  };
};

// Middleware to validate socket event data
export const validateEventData = <T>(validator: (data: any) => data is T) => {
  return (data: any, socket: AuthenticatedSocket, next: (err?: ExtendedError) => void) => {
    if (!validator(data)) {
      const error: ExtendedError = new Error("Invalid event data");
      error.data = { code: "INVALID_DATA", message: "Event data validation failed" };
      return next(error);
    }
    next();
  };
};

// Rate limiting for socket events
const eventCounts = new Map<string, { count: number; resetTime: number }>();

export const socketRateLimit = (maxEvents: number = 100, windowMs: number = 60000) => {
  return (socket: AuthenticatedSocket, next: (err?: ExtendedError) => void) => {
    const key = `${socket.userId}_${socket.tenantId}`;
    const now = Date.now();
    const userEvents = eventCounts.get(key);

    if (!userEvents || now > userEvents.resetTime) {
      eventCounts.set(key, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (userEvents.count >= maxEvents) {
      const error: ExtendedError = new Error("Rate limit exceeded");
      error.data = { code: "RATE_LIMIT_EXCEEDED", message: "Too many events sent" };
      return next(error);
    }

    userEvents.count++;
    next();
  };
};

// Cleanup expired rate limit entries
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of eventCounts.entries()) {
    if (now > data.resetTime) {
      eventCounts.delete(key);
    }
  }
}, 60000); // Clean up every minute

// Helper function to emit error to socket
export const emitSocketError = (socket: Socket, error: SocketError) => {
  socket.emit(SOCKET_EVENTS.ERROR, error);
  Logger.warn(`Socket error emitted to ${socket.id}:`, error);
};

// Helper function to emit validation error
export const emitValidationError = (socket: Socket, message: string, details?: any) => {
  const error: SocketError = {
    code: "VALIDATION_ERROR",
    message,
    details,
  };
  socket.emit(SOCKET_EVENTS.VALIDATION_ERROR, error);
  Logger.warn(`Validation error emitted to ${socket.id}:`, error);
};

// Middleware to log socket events
export const socketEventLogger = (eventName: string) => {
  return (socket: AuthenticatedSocket, data: any, next: (err?: ExtendedError) => void) => {
    Logger.info(`Socket event ${eventName} from user ${socket.userId} (${socket.id})`, {
      userId: socket.userId,
      tenantId: socket.tenantId,
      eventName,
      socketId: socket.id,
      dataKeys: data ? Object.keys(data) : [],
    });
    next();
  };
};
