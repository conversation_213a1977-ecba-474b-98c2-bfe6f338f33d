const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

const baseURL = 'http://localhost:5000';

async function testFileSize() {
  console.log('📏 Testing File Size Limits...\n');

  try {
    // Login first
    const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'AdminPassword123!'
    });
    const token = loginResponse.data.accessToken;
    console.log('✅ Logged in successfully');

    // Test 1: Normal size file (should pass)
    console.log('\n1. Testing normal size file...');
    const normalFile = Buffer.alloc(1024, 0); // 1KB
    fs.writeFileSync('test-normal.png', normalFile);
    
    const form1 = new FormData();
    form1.append('profilePicture', fs.createReadStream('test-normal.png'));
    
    try {
      const response1 = await axios.post(`${baseURL}/api/users/profile/picture`, form1, {
        headers: { ...form1.getHeaders(), 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ Normal size file accepted');
    } catch (error) {
      console.log('⚠️  Normal file rejected (might be due to invalid format):', error.response?.data?.message);
    }

    // Test 2: Large file (should be rejected by multer)
    console.log('\n2. Testing oversized file (12MB)...');
    const largeFile = Buffer.alloc(12 * 1024 * 1024, 0); // 12MB (over 10MB limit)
    fs.writeFileSync('test-large.png', largeFile);
    
    const form2 = new FormData();
    form2.append('profilePicture', fs.createReadStream('test-large.png'));
    
    try {
      await axios.post(`${baseURL}/api/users/profile/picture`, form2, {
        headers: { ...form2.getHeaders(), 'Authorization': `Bearer ${token}` }
      });
      console.log('❌ Large file should have been rejected');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Large file properly rejected:', error.response.data.message || error.response.data.error);
      } else {
        console.log('⚠️  Unexpected error:', error.message);
      }
    }

    // Cleanup
    if (fs.existsSync('test-normal.png')) fs.unlinkSync('test-normal.png');
    if (fs.existsSync('test-large.png')) fs.unlinkSync('test-large.png');
    console.log('\n✅ File size validation test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

testFileSize();
