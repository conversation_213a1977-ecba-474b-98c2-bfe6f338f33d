const axios = require('axios');
const fs = require('fs');
const path = require('path');

const baseURL = 'http://localhost:5000';

async function testStaticServing() {
  console.log('🌐 Testing Static File Serving...\n');

  try {
    console.log('1. Checking if uploads directory is accessible...');
    
    // Test accessing the uploads root (should return directory listing or 404)
    try {
      const response = await axios.get(`${baseURL}/uploads/`);
      console.log('✅ Uploads root accessible');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ Uploads root properly protected (404)');
      } else {
        console.log('⚠️  Unexpected response:', error.response?.status);
      }
    }

    console.log('\n2. Testing existing image file access...');
    
    // Check if there are any existing images
    const profilesDir = path.join(__dirname, 'uploads', 'profiles');
    if (fs.existsSync(profilesDir)) {
      const files = fs.readdirSync(profilesDir);
      const imageFiles = files.filter(f => f.match(/\.(png|jpg|jpeg|gif|webp)$/i));
      
      if (imageFiles.length > 0) {
        const testImage = imageFiles[0];
        const imageUrl = `/uploads/profiles/${testImage}`;
        
        console.log(`   Testing: ${imageUrl}`);
        
        const response = await axios.get(`${baseURL}${imageUrl}`);
        console.log('✅ Image file accessible');
        console.log(`   Status: ${response.status}`);
        console.log(`   Content-Type: ${response.headers['content-type']}`);
        console.log(`   Content-Length: ${response.headers['content-length']} bytes`);
        console.log(`   Cache-Control: ${response.headers['cache-control'] || 'Not set'}`);
        
        // Verify it's actually an image
        if (response.headers['content-type']?.startsWith('image/')) {
          console.log('✅ Correct MIME type for image');
        } else {
          console.log('⚠️  Unexpected MIME type');
        }
        
      } else {
        console.log('⚠️  No image files found to test');
      }
    }

    console.log('\n3. Testing non-existent file access...');
    try {
      await axios.get(`${baseURL}/uploads/profiles/non-existent-file.png`);
      console.log('❌ Non-existent file should return 404');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ Non-existent file properly returns 404');
      } else {
        console.log('⚠️  Unexpected status:', error.response?.status);
      }
    }

    console.log('\n4. Testing directory traversal protection...');
    const maliciousUrls = [
      '/uploads/../package.json',
      '/uploads/profiles/../../package.json',
      '/uploads/profiles/../../../etc/passwd',
      '/uploads/profiles/..%2F..%2Fpackage.json'
    ];

    for (const url of maliciousUrls) {
      try {
        await axios.get(`${baseURL}${url}`);
        console.log(`❌ Directory traversal should be blocked: ${url}`);
      } catch (error) {
        if (error.response && (error.response.status === 404 || error.response.status === 403)) {
          console.log(`✅ Directory traversal blocked: ${url}`);
        } else {
          console.log(`⚠️  Unexpected response for ${url}:`, error.response?.status);
        }
      }
    }

    console.log('\n5. Testing different file types...');
    
    // Create test files of different types
    const testFiles = [
      { name: 'test.png', content: Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==', 'base64'), type: 'image/png' },
      { name: 'test.txt', content: Buffer.from('test file'), type: 'text/plain' },
      { name: 'test.js', content: Buffer.from('console.log("test");'), type: 'application/javascript' }
    ];

    for (const testFile of testFiles) {
      const filePath = path.join(__dirname, 'uploads', 'temp', testFile.name);
      fs.writeFileSync(filePath, testFile.content);
      
      try {
        const response = await axios.get(`${baseURL}/uploads/temp/${testFile.name}`);
        console.log(`✅ ${testFile.name} accessible`);
        console.log(`   Content-Type: ${response.headers['content-type']}`);
        
        // Verify content type
        if (response.headers['content-type']?.includes(testFile.type.split('/')[0])) {
          console.log(`   ✅ Correct MIME type detected`);
        }
        
      } catch (error) {
        console.log(`⚠️  ${testFile.name} not accessible:`, error.response?.status);
      }
      
      // Cleanup
      fs.unlinkSync(filePath);
    }

    console.log('\n6. Testing HTTP headers and caching...');
    
    if (fs.existsSync(profilesDir)) {
      const files = fs.readdirSync(profilesDir);
      if (files.length > 0) {
        const testImage = files[0];
        const response = await axios.get(`${baseURL}/uploads/profiles/${testImage}`);
        
        console.log('✅ HTTP Headers:');
        console.log(`   Content-Type: ${response.headers['content-type']}`);
        console.log(`   Content-Length: ${response.headers['content-length']}`);
        console.log(`   Last-Modified: ${response.headers['last-modified'] || 'Not set'}`);
        console.log(`   ETag: ${response.headers['etag'] || 'Not set'}`);
        console.log(`   Cache-Control: ${response.headers['cache-control'] || 'Not set'}`);
        
        // Test conditional requests
        if (response.headers['etag']) {
          try {
            const conditionalResponse = await axios.get(`${baseURL}/uploads/profiles/${testImage}`, {
              headers: { 'If-None-Match': response.headers['etag'] }
            });
            console.log('⚠️  Expected 304 Not Modified');
          } catch (error) {
            if (error.response && error.response.status === 304) {
              console.log('✅ Conditional requests working (304 Not Modified)');
            }
          }
        }
      }
    }

    console.log('\n7. Testing concurrent access...');
    
    if (fs.existsSync(profilesDir)) {
      const files = fs.readdirSync(profilesDir);
      if (files.length > 0) {
        const testImage = files[0];
        const imageUrl = `${baseURL}/uploads/profiles/${testImage}`;
        
        // Make 5 concurrent requests
        const promises = Array(5).fill().map(() => axios.get(imageUrl));
        
        const startTime = Date.now();
        const responses = await Promise.all(promises);
        const endTime = Date.now();
        
        console.log(`✅ ${responses.length} concurrent requests completed`);
        console.log(`   Total time: ${endTime - startTime}ms`);
        console.log(`   Average time: ${(endTime - startTime) / responses.length}ms per request`);
        
        // Verify all responses are successful
        const allSuccessful = responses.every(r => r.status === 200);
        if (allSuccessful) {
          console.log('✅ All concurrent requests successful');
        } else {
          console.log('⚠️  Some concurrent requests failed');
        }
      }
    }

    console.log('\n🎉 Static file serving test completed successfully!');
    
    console.log('\n📋 Static File Serving Summary:');
    console.log('✅ Static file serving configured correctly');
    console.log('✅ Image files accessible via HTTP');
    console.log('✅ Proper MIME type detection');
    console.log('✅ 404 handling for non-existent files');
    console.log('✅ Directory traversal protection');
    console.log('✅ Multiple file type support');
    console.log('✅ HTTP headers properly set');
    console.log('✅ Concurrent access handling');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
    }
  }
}

testStaticServing();
