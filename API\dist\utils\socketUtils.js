"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocketUtils = void 0;
const socketService_1 = require("../services/socketService");
const notificationService_1 = require("../services/notificationService");
const socket_1 = require("../config/socket");
const socket_2 = require("../config/socket");
const socket_3 = require("../types/socket");
const loggerService_1 = require("../services/loggerService");
class SocketUtils {
    static async triggerFollowRequest(followerId, followingId, tenantId) {
        try {
            await notificationService_1.notificationService.sendFollowRequestNotification(followerId, followingId, tenantId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering follow request:", error);
        }
    }
    static async triggerFollowAccepted(followerId, followingId, tenantId) {
        try {
            await notificationService_1.notificationService.sendFollowAcceptedNotification(followerId, followingId, tenantId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering follow accepted:", error);
        }
    }
    static async triggerConnectionRequest(requesterId, targetId, tenantId) {
        try {
            await notificationService_1.notificationService.sendConnectionRequestNotification(requesterId, targetId, tenantId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering connection request:", error);
        }
    }
    static async triggerConnectionAccepted(requesterId, accepterId, tenantId) {
        try {
            await notificationService_1.notificationService.sendConnectionAcceptedNotification(requesterId, accepterId, tenantId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering connection accepted:", error);
        }
    }
    static async triggerMessageNotification(senderId, receiverId, tenantId, content) {
        try {
            await notificationService_1.notificationService.sendMessageNotification(senderId, receiverId, tenantId, content);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering message notification:", error);
        }
    }
    static async triggerEventCreated(eventId, tenantId, authorId) {
        try {
            await notificationService_1.notificationService.sendEventNotification(eventId, tenantId, socket_3.EventUpdateType.CREATED, authorId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering event created:", error);
        }
    }
    static async triggerEventUpdated(eventId, tenantId, authorId) {
        try {
            await notificationService_1.notificationService.sendEventNotification(eventId, tenantId, socket_3.EventUpdateType.UPDATED, authorId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering event updated:", error);
        }
    }
    static async triggerEventDeleted(eventId, tenantId, authorId) {
        try {
            await notificationService_1.notificationService.sendEventNotification(eventId, tenantId, socket_3.EventUpdateType.DELETED, authorId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering event deleted:", error);
        }
    }
    static async triggerJobPosted(jobId, tenantId, authorId) {
        try {
            await notificationService_1.notificationService.sendJobNotification(jobId, tenantId, socket_3.JobUpdateType.POSTED, authorId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering job posted:", error);
        }
    }
    static async triggerJobUpdated(jobId, tenantId, authorId) {
        try {
            await notificationService_1.notificationService.sendJobNotification(jobId, tenantId, socket_3.JobUpdateType.UPDATED, authorId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering job updated:", error);
        }
    }
    static async triggerJobDeleted(jobId, tenantId, authorId) {
        try {
            await notificationService_1.notificationService.sendJobNotification(jobId, tenantId, socket_3.JobUpdateType.DELETED, authorId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering job deleted:", error);
        }
    }
    static async triggerPostLiked(postId, tenantId, likerId, postAuthorId) {
        try {
            await notificationService_1.notificationService.sendPostNotification(postId, tenantId, socket_3.PostUpdateType.LIKED, likerId, postAuthorId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering post liked:", error);
        }
    }
    static async triggerPostCommented(postId, tenantId, commenterId, postAuthorId) {
        try {
            await notificationService_1.notificationService.sendPostNotification(postId, tenantId, socket_3.PostUpdateType.COMMENTED, commenterId, postAuthorId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering post commented:", error);
        }
    }
    static async triggerSystemNotification(tenantId, title, message, data) {
        try {
            await notificationService_1.notificationService.sendSystemNotification(tenantId, title, message, data);
        }
        catch (error) {
            loggerService_1.Logger.error("Error triggering system notification:", error);
        }
    }
    static getOnlineUsers(tenantId) {
        try {
            return socketService_1.socketService.getOnlineUsersForTenant(tenantId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error getting online users:", error);
            return [];
        }
    }
    static isUserOnline(userId) {
        try {
            return socketService_1.socketService.isUserOnline(userId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error checking if user is online:", error);
            return false;
        }
    }
    static async sendCustomNotification(userId, tenantId, type, title, message, data) {
        try {
            const notification = {
                userId,
                tenantId,
                type,
                title,
                message,
                data,
                createdAt: new Date(),
            };
            await socketService_1.socketService.sendNotificationToUser(userId, notification);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending custom notification:", error);
        }
    }
    static async sendCustomNotificationToUsers(userIds, tenantId, type, title, message, data) {
        try {
            const notification = {
                userId: 0,
                tenantId,
                type,
                title,
                message,
                data,
                createdAt: new Date(),
            };
            await socketService_1.socketService.sendNotificationToUsers(userIds, notification);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending custom notification to users:", error);
        }
    }
    static async broadcastToTenant(tenantId, event, data) {
        try {
            const io = (0, socket_1.getSocketIO)();
            io.to(socket_2.SOCKET_ROOMS.TENANT(tenantId)).emit(event, data);
            loggerService_1.Logger.info(`Broadcasted ${event} to tenant ${tenantId}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error broadcasting to tenant:", error);
        }
    }
    static async broadcastToUsers(userIds, event, data) {
        try {
            const io = (0, socket_1.getSocketIO)();
            userIds.forEach(userId => {
                io.to(socket_2.SOCKET_ROOMS.USER(userId)).emit(event, data);
            });
            loggerService_1.Logger.info(`Broadcasted ${event} to ${userIds.length} users`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error broadcasting to users:", error);
        }
    }
    static async sendActivityUpdate(tenantId, activityType, userId, data) {
        try {
            const activityData = {
                type: activityType,
                userId,
                timestamp: new Date(),
                data,
            };
            await this.broadcastToTenant(tenantId, "activity_update", activityData);
        }
        catch (error) {
            loggerService_1.Logger.error("Error sending activity update:", error);
        }
    }
    static async getRoomMembers(roomId) {
        try {
            return await socketService_1.socketService.getRoomMembers(roomId);
        }
        catch (error) {
            loggerService_1.Logger.error("Error getting room members:", error);
            return [];
        }
    }
    static async forceDisconnectUser(userId, reason) {
        try {
            const io = (0, socket_1.getSocketIO)();
            const socketIds = socketService_1.socketService.getUserSocketIds(userId);
            socketIds.forEach(socketId => {
                const socket = io.sockets.sockets.get(socketId);
                if (socket) {
                    socket.emit(socket_2.SOCKET_EVENTS.ERROR, {
                        code: "FORCE_DISCONNECT",
                        message: reason || "You have been disconnected by an administrator",
                    });
                    socket.disconnect(true);
                }
            });
            loggerService_1.Logger.info(`Force disconnected user ${userId}, reason: ${reason || "Admin action"}`);
        }
        catch (error) {
            loggerService_1.Logger.error("Error force disconnecting user:", error);
        }
    }
    static getServerStats() {
        try {
            const io = (0, socket_1.getSocketIO)();
            const onlineUsers = socketService_1.socketService.getOnlineUsersForTenant(0);
            return {
                totalConnections: io.engine.clientsCount,
                totalOnlineUsers: onlineUsers.length,
                serverUptime: process.uptime(),
                timestamp: new Date(),
            };
        }
        catch (error) {
            loggerService_1.Logger.error("Error getting server stats:", error);
            return {
                totalConnections: 0,
                totalOnlineUsers: 0,
                serverUptime: 0,
                timestamp: new Date(),
            };
        }
    }
}
exports.SocketUtils = SocketUtils;
exports.default = SocketUtils;
//# sourceMappingURL=socketUtils.js.map