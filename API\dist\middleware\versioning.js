"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getVersionInfo = exports.versionAnalytics = exports.contentNegotiation = exports.versionMigration = exports.deprecationWarning = exports.versionedRoute = exports.apiVersioning = void 0;
const loggerService_1 = require("../services/loggerService");
const API_VERSIONS = {
    v1: "1.0.0",
    v2: "2.0.0",
};
const DEFAULT_VERSION = "v1";
const VERSION_COMPATIBILITY = {
    v1: ["v1"],
    v2: ["v2", "v1"],
};
const apiVersioning = () => {
    return (req, res, next) => {
        let requestedVersion;
        let apiVersion = DEFAULT_VERSION;
        const headerVersion = req.get("API-Version") || req.get("X-API-Version");
        if (headerVersion) {
            requestedVersion = headerVersion;
        }
        const acceptHeader = req.get("Accept");
        if (acceptHeader && !requestedVersion) {
            const versionMatch = acceptHeader.match(/application\/vnd\.alumni-portal\.([^+]+)/);
            if (versionMatch) {
                requestedVersion = versionMatch[1];
            }
        }
        const pathMatch = req.path.match(/^\/api\/(v\d+)\//);
        if (pathMatch && !requestedVersion) {
            requestedVersion = pathMatch[1];
        }
        if (req.query.version && !requestedVersion) {
            requestedVersion = String(req.query.version);
        }
        if (requestedVersion) {
            const normalizedVersion = requestedVersion.toLowerCase();
            if (API_VERSIONS[normalizedVersion]) {
                apiVersion = normalizedVersion;
            }
            else {
                const compatibleVersion = findCompatibleVersion(requestedVersion);
                if (compatibleVersion) {
                    apiVersion = compatibleVersion;
                    loggerService_1.Logger.warn(`API version ${requestedVersion} not found, using compatible version ${apiVersion}`, {
                        requestedVersion,
                        compatibleVersion: apiVersion,
                        path: req.path,
                        userAgent: req.get("User-Agent"),
                    });
                }
                else {
                    return res.status(400).json({
                        error: "Unsupported API version",
                        requestedVersion,
                        supportedVersions: Object.keys(API_VERSIONS),
                        message: `API version '${requestedVersion}' is not supported. Please use one of: ${Object.keys(API_VERSIONS).join(", ")}`,
                    });
                }
            }
        }
        req.apiVersion = apiVersion;
        req.requestedVersion = requestedVersion;
        res.set("API-Version", API_VERSIONS[apiVersion]);
        res.set("X-API-Version", apiVersion);
        res.set("X-Supported-Versions", Object.keys(API_VERSIONS).join(", "));
        loggerService_1.Logger.api(`API version ${apiVersion} used`, req.path, req.method, {
            version: apiVersion,
            requestedVersion,
            userAgent: req.get("User-Agent"),
        });
        return next();
    };
};
exports.apiVersioning = apiVersioning;
function findCompatibleVersion(requestedVersion) {
    const normalizedRequested = requestedVersion.toLowerCase();
    if (API_VERSIONS[normalizedRequested]) {
        return normalizedRequested;
    }
    if (/^\d+$/.test(normalizedRequested)) {
        const versionKey = `v${normalizedRequested}`;
        if (API_VERSIONS[versionKey]) {
            return versionKey;
        }
    }
    const semverMatch = normalizedRequested.match(/^(\d+)\./);
    if (semverMatch) {
        const majorVersion = `v${semverMatch[1]}`;
        if (API_VERSIONS[majorVersion]) {
            return majorVersion;
        }
    }
    return null;
}
const versionedRoute = (handlers) => {
    return (req, res, next) => {
        const version = req.apiVersion || DEFAULT_VERSION;
        let handler = handlers[version];
        if (!handler) {
            const compatibleVersions = VERSION_COMPATIBILITY[version] || [version];
            let foundHandler = null;
            for (const compatibleVersion of compatibleVersions) {
                if (handlers[compatibleVersion]) {
                    foundHandler = handlers[compatibleVersion];
                    break;
                }
            }
            if (!foundHandler) {
                return res.status(501).json({
                    error: "Version not implemented",
                    version,
                    message: `This endpoint is not implemented for API version ${version}`,
                    availableVersions: Object.keys(handlers),
                });
            }
            handler = foundHandler;
        }
        if (typeof handler === "function") {
            return handler(req, res, next);
        }
        else {
            return next();
        }
    };
};
exports.versionedRoute = versionedRoute;
const deprecationWarning = (version, deprecatedIn, removedIn, message) => {
    return (req, res, next) => {
        if (req.apiVersion === version) {
            const warningMessage = message || `API version ${version} is deprecated and will be removed in version ${removedIn}`;
            res.set("Warning", `299 - "Deprecated API" "${warningMessage}"`);
            res.set("Deprecation", "true");
            res.set("Sunset", removedIn);
            loggerService_1.Logger.warn(`Deprecated API version used: ${version}`, {
                path: req.path,
                method: req.method,
                userAgent: req.get("User-Agent"),
                deprecatedIn,
                removedIn,
            });
        }
        next();
    };
};
exports.deprecationWarning = deprecationWarning;
const versionMigration = (fromVersion, toVersion, migrationFn) => {
    return (req, res, next) => {
        if (req.apiVersion === fromVersion) {
            const originalJson = res.json.bind(res);
            res.json = function (data) {
                try {
                    const migratedData = migrationFn(data);
                    return originalJson(migratedData);
                }
                catch (error) {
                    loggerService_1.Logger.error(`Version migration failed from ${fromVersion} to ${toVersion}`, {
                        error: error instanceof Error ? error.message : String(error),
                        path: req.path,
                    });
                    return originalJson(data);
                }
            };
        }
        next();
    };
};
exports.versionMigration = versionMigration;
const contentNegotiation = () => {
    return (req, res, next) => {
        const version = req.apiVersion || DEFAULT_VERSION;
        const contentType = version === "v2" ? "application/vnd.alumni-portal.v2+json" : "application/vnd.alumni-portal.v1+json";
        res.set("Content-Type", contentType);
        next();
    };
};
exports.contentNegotiation = contentNegotiation;
const versionAnalytics = () => {
    const versionUsage = new Map();
    setInterval(() => {
        if (versionUsage.size > 0) {
            loggerService_1.Logger.info("[ANALYTICS] API version usage", Object.fromEntries(versionUsage));
            versionUsage.clear();
        }
    }, 3600000);
    return (req, _res, next) => {
        const version = req.apiVersion || DEFAULT_VERSION;
        const key = `${version}_${req.method}_${req.route?.path || req.path}`;
        versionUsage.set(key, (versionUsage.get(key) || 0) + 1);
        next();
    };
};
exports.versionAnalytics = versionAnalytics;
const getVersionInfo = () => {
    return {
        versions: API_VERSIONS,
        defaultVersion: DEFAULT_VERSION,
        compatibility: VERSION_COMPATIBILITY,
        supportedVersions: Object.keys(API_VERSIONS),
    };
};
exports.getVersionInfo = getVersionInfo;
//# sourceMappingURL=versioning.js.map