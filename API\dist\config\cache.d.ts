import NodeCache from "node-cache";
export declare const cache: NodeCache;
export declare const sessionCache: NodeCache;
export declare const rateLimitCache: NodeCache;
export declare const checkCacheHealth: () => boolean;
export declare class CacheService {
    private static readonly DEFAULT_TTL;
    static get<T>(key: string): T | null;
    static getAsync<T>(key: string): Promise<T | null>;
    static set(key: string, data: any, ttl?: number): boolean;
    static setAsync(key: string, data: any, ttl?: number): Promise<boolean>;
    static del(key: string): boolean;
    static delAsync(key: string): Promise<boolean>;
    static delPattern(pattern: string): number;
    static delPatternAsync(pattern: string): Promise<number>;
    static exists(key: string): boolean;
    static existsAsync(key: string): Promise<boolean>;
    static incr(key: string, ttl?: number): number;
    static incrAsync(key: string, ttl?: number): Promise<number>;
    static getOrSet<T>(key: string, fetchFunction: () => Promise<T>, ttl?: number): Promise<T | null>;
    static mget<T>(keys: string[]): Map<string, T | null>;
    static mgetAsync<T>(keys: string[]): Promise<Map<string, T | null>>;
    static mset(entries: Array<{
        key: string;
        data: any;
        ttl?: number;
    }>): boolean;
    static msetAsync(entries: Array<{
        key: string;
        data: any;
        ttl?: number;
    }>): Promise<boolean>;
    static deletePattern(pattern: string): number;
    static deletePatternAsync(pattern: string): Promise<number>;
    static clear(): boolean;
    static clearAsync(): Promise<boolean>;
    static getSize(): number;
    static getSizeAsync(): Promise<number>;
    static getStats(): any;
    static getStatsAsync(): Promise<any>;
    static getKeys(): string[];
}
export declare const CacheKeys: {
    user: (userId: string) => string;
    userProfile: (userId: string) => string;
    userConnections: (userId: string) => string;
    job: (jobId: string) => string;
    jobsList: (page: number, filters: string) => string;
    event: (eventId: string) => string;
    eventsList: (page: number, filters: string) => string;
    post: (postId: string) => string;
    postsList: (page: number, filters: string) => string;
    notifications: (userId: string) => string;
    dashboardMetrics: () => string;
    userSearch: (query: string, page: number) => string;
    rateLimitUser: (userId: string) => string;
    rateLimitIP: (ip: string) => string;
    session: (sessionId: string) => string;
};
export default cache;
//# sourceMappingURL=cache.d.ts.map