const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    console.log('Checking existing users in database...\n');
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        full_name: true,
        role: true,
        account_status: true,
        created_at: true
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    if (users.length === 0) {
      console.log('❌ No users found in database');
      
      // Create a test admin user directly
      console.log('\n🔧 Creating test admin user...');
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash('AdminPassword123!', 12);
      
      const adminUser = await prisma.user.create({
        data: {
          tenant_id: 1,
          full_name: 'Test Admin',
          email: '<EMAIL>',
          password_hash: hashedPassword,
          usn: 'ADMIN001',
          role: 'TENANT_ADMIN',
          account_status: 'APPROVED'
        }
      });
      
      console.log('✅ Test admin user created:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: AdminPassword123!');
      console.log('   Role:', adminUser.role);
      console.log('   Status:', adminUser.account_status);
      
    } else {
      console.log(`✅ Found ${users.length} users:`);
      users.forEach((user, index) => {
        console.log(`\n${index + 1}. ${user.full_name}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Role: ${user.role}`);
        console.log(`   Status: ${user.account_status}`);
        console.log(`   Created: ${user.created_at}`);
      });
      
      // Find an approved admin user
      const approvedAdmin = users.find(u => u.role === 'TENANT_ADMIN' && u.account_status === 'APPROVED');
      if (approvedAdmin) {
        console.log(`\n✅ Found approved admin: ${approvedAdmin.email}`);
      } else {
        console.log('\n⚠️  No approved admin users found');
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
