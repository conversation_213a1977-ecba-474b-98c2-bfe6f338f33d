import fs from "fs";
export declare class FileService {
    private static uploadDir;
    static getFilePath(relativePath: string): string;
    static getFileUrl(relativePath: string): string;
    static deleteFile(relativePath: string): Promise<boolean>;
    static fileExists(relativePath: string): boolean;
    static getFileStats(relativePath: string): fs.Stats | null;
    static moveFile(tempPath: string, permanentPath: string): Promise<boolean>;
    static cleanupTempFiles(maxAgeHours?: number): Promise<void>;
    static getRelativePath(absolutePath: string): string;
    static validateImageFile(file: Express.Multer.File): {
        isValid: boolean;
        error?: string;
    };
    static generateUniqueFilename(originalName: string, prefix?: string): string;
    static initializeCleanup(): void;
}
//# sourceMappingURL=fileService.d.ts.map