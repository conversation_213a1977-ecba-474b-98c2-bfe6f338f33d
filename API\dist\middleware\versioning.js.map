{"version": 3, "file": "versioning.js", "sourceRoot": "", "sources": ["../../src/middleware/versioning.ts"], "names": [], "mappings": ";;;AACA,6DAAmD;AAGnD,MAAM,YAAY,GAAG;IACnB,EAAE,EAAE,OAAO;IACX,EAAE,EAAE,OAAO;CACH,CAAC;AAKX,MAAM,eAAe,GAAe,IAAI,CAAC;AAGzC,MAAM,qBAAqB,GAAG;IAC5B,EAAE,EAAE,CAAC,IAAI,CAAC;IACV,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;CACR,CAAC;AAaJ,MAAM,aAAa,GAAG,GAAG,EAAE;IAChC,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,gBAAoC,CAAC;QACzC,IAAI,UAAU,GAAe,eAAe,CAAC;QAG7C,MAAM,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACzE,IAAI,aAAa,EAAE,CAAC;YAClB,gBAAgB,GAAG,aAAa,CAAC;QACnC,CAAC;QAGD,MAAM,YAAY,GAAG,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,YAAY,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtC,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YACpF,IAAI,YAAY,EAAE,CAAC;gBACjB,gBAAgB,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAGD,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QACrD,IAAI,SAAS,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACnC,gBAAgB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC;QAGD,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3C,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;QAGD,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,WAAW,EAAgB,CAAC;YACvE,IAAI,YAAY,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBACpC,UAAU,GAAG,iBAAiB,CAAC;YACjC,CAAC;iBAAM,CAAC;gBAEN,MAAM,iBAAiB,GAAG,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;gBAClE,IAAI,iBAAiB,EAAE,CAAC;oBACtB,UAAU,GAAG,iBAAiB,CAAC;oBAC/B,sBAAM,CAAC,IAAI,CAAC,eAAe,gBAAgB,wCAAwC,UAAU,EAAE,EAAE;wBAC/F,gBAAgB;wBAChB,iBAAiB,EAAE,UAAU;wBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;wBACd,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;qBACjC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBAEN,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,KAAK,EAAE,yBAAyB;wBAChC,gBAAgB;wBAChB,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;wBAC5C,OAAO,EAAE,gBAAgB,gBAAgB,0CAA0C,MAAM,CAAC,IAAI,CAC5F,YAAY,CACb,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;qBACf,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAGD,GAAG,CAAC,UAAU,GAAG,UAAU,CAAC;QAC5B,GAAG,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QAGxC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QACjD,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;QACrC,GAAG,CAAC,GAAG,CAAC,sBAAsB,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAGtE,sBAAM,CAAC,GAAG,CAAC,eAAe,UAAU,OAAO,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE;YACjE,OAAO,EAAE,UAAU;YACnB,gBAAgB;YAChB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;SACjC,CAAC,CAAC;QAEH,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC,CAAC;AA/EW,QAAA,aAAa,iBA+ExB;AAGF,SAAS,qBAAqB,CAAC,gBAAwB;IACrD,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,WAAW,EAAE,CAAC;IAG3D,IAAI,YAAY,CAAC,mBAAiC,CAAC,EAAE,CAAC;QACpD,OAAO,mBAAiC,CAAC;IAC3C,CAAC;IAGD,IAAI,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC;QACtC,MAAM,UAAU,GAAG,IAAI,mBAAmB,EAAgB,CAAC;QAC3D,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7B,OAAO,UAAU,CAAC;QACpB,CAAC;IACH,CAAC;IAGD,MAAM,WAAW,GAAG,mBAAmB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC1D,IAAI,WAAW,EAAE,CAAC;QAChB,MAAM,YAAY,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,EAAgB,CAAC;QACxD,IAAI,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC;YAC/B,OAAO,YAAY,CAAC;QACtB,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAGM,MAAM,cAAc,GAAG,CAAC,QAA0C,EAAE,EAAE;IAC3E,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,IAAI,eAAe,CAAC;QAClD,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;QAEhC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEb,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACvE,IAAI,YAAY,GAAG,IAAI,CAAC;YAExB,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE,CAAC;gBACnD,IAAI,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;oBAChC,YAAY,GAAG,QAAQ,CAAC,iBAAiB,CAAC,CAAC;oBAC3C,MAAM;gBACR,CAAC;YACH,CAAC;YAED,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,KAAK,EAAE,yBAAyB;oBAChC,OAAO;oBACP,OAAO,EAAE,oDAAoD,OAAO,EAAE;oBACtE,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;iBACzC,CAAC,CAAC;YACL,CAAC;YAED,OAAO,GAAG,YAAY,CAAC;QACzB,CAAC;QAGD,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;YAClC,OAAO,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AApCW,QAAA,cAAc,kBAoCzB;AAGK,MAAM,kBAAkB,GAAG,CAAC,OAAmB,EAAE,YAAoB,EAAE,SAAiB,EAAE,OAAgB,EAAE,EAAE;IACnH,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,GAAG,CAAC,UAAU,KAAK,OAAO,EAAE,CAAC;YAC/B,MAAM,cAAc,GAClB,OAAO,IAAI,eAAe,OAAO,iDAAiD,SAAS,EAAE,CAAC;YAEhG,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,2BAA2B,cAAc,GAAG,CAAC,CAAC;YACjE,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAC/B,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAE7B,sBAAM,CAAC,IAAI,CAAC,gCAAgC,OAAO,EAAE,EAAE;gBACrD,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;gBAChC,YAAY;gBACZ,SAAS;aACV,CAAC,CAAC;QACL,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AArBW,QAAA,kBAAkB,sBAqB7B;AAGK,MAAM,gBAAgB,GAAG,CAAC,WAAuB,EAAE,SAAqB,EAAE,WAA+B,EAAE,EAAE;IAClH,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,IAAI,GAAG,CAAC,UAAU,KAAK,WAAW,EAAE,CAAC;YAEnC,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAGxC,GAAG,CAAC,IAAI,GAAG,UAAU,IAAS;gBAC5B,IAAI,CAAC;oBACH,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;oBACvC,OAAO,YAAY,CAAC,YAAY,CAAC,CAAC;gBACpC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,sBAAM,CAAC,KAAK,CAAC,iCAAiC,WAAW,OAAO,SAAS,EAAE,EAAE;wBAC3E,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;wBAC7D,IAAI,EAAE,GAAG,CAAC,IAAI;qBACf,CAAC,CAAC;oBACH,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;gBAC5B,CAAC;YACH,CAAC,CAAC;QACJ,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAvBW,QAAA,gBAAgB,oBAuB3B;AAGK,MAAM,kBAAkB,GAAG,GAAG,EAAE;IACrC,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,IAAI,eAAe,CAAC;QAGlD,MAAM,WAAW,GACf,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,uCAAuC,CAAC,CAAC,CAAC,uCAAuC,CAAC;QAEvG,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAErC,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,kBAAkB,sBAY7B;AAGK,MAAM,gBAAgB,GAAG,GAAG,EAAE;IACnC,MAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAC;IAG/C,WAAW,CAAC,GAAG,EAAE;QACf,IAAI,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC1B,sBAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;YAC/E,YAAY,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;IACH,CAAC,EAAE,OAAO,CAAC,CAAC;IAEZ,OAAO,CAAC,GAAY,EAAE,IAAc,EAAE,IAAkB,EAAE,EAAE;QAC1D,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,IAAI,eAAe,CAAC;QAClD,MAAM,GAAG,GAAG,GAAG,OAAO,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QAEtE,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAExD,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAnBW,QAAA,gBAAgB,oBAmB3B;AAGK,MAAM,cAAc,GAAG,GAAG,EAAE;IACjC,OAAO;QACL,QAAQ,EAAE,YAAY;QACtB,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,qBAAqB;QACpC,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;KAC7C,CAAC;AACJ,CAAC,CAAC;AAPW,QAAA,cAAc,kBAOzB"}