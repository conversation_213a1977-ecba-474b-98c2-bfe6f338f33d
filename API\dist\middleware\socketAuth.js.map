{"version": 3, "file": "socketAuth.js", "sourceRoot": "", "sources": ["../../src/middleware/socketAuth.ts"], "names": [], "mappings": ";;;;;;AAAA,gEAA+B;AAG/B,2CAA8C;AAE9C,6CAAiD;AACjD,6DAAmD;AAEnD,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAY3B,MAAM,oBAAoB,GAAG,KAAK,EACvC,MAA2B,EAC3B,IAAmC,EACnC,EAAE;IACF,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAE9G,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,KAAK,GAAkB,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACxE,KAAK,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;YAC/E,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAGD,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAW,CAAe,CAAC;QAEzE,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACzC,MAAM,KAAK,GAAkB,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAChE,KAAK,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;YAC5E,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAGD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACvC,KAAK,EAAE;gBACL,EAAE,EAAE,OAAO,CAAC,MAAM;gBAClB,SAAS,EAAE,OAAO,CAAC,QAAQ;gBAC3B,cAAc,EAAE,UAAU;aAC3B;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE,IAAI;wBACV,SAAS,EAAE,IAAI;qBAChB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,KAAK,GAAkB,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACzE,KAAK,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,wCAAwC,EAAE,CAAC;YAC3F,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAkB,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC/D,KAAK,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;YAClF,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAGD,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;QACxB,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC5B,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;QAE9B,sBAAM,CAAC,IAAI,CAAC,iCAAiC,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,eAAe,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACxG,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,sBAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QAEpD,IAAI,YAAY,GAAG,uBAAuB,CAAC;QAC3C,IAAI,SAAS,GAAG,aAAa,CAAC;QAE9B,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,YAAY,GAAG,eAAe,CAAC;YAC/B,SAAS,GAAG,eAAe,CAAC;QAC9B,CAAC;aAAM,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAClD,YAAY,GAAG,eAAe,CAAC;YAC/B,SAAS,GAAG,eAAe,CAAC;QAC9B,CAAC;QAED,MAAM,SAAS,GAAkB,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QACzD,SAAS,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;QAC5D,IAAI,CAAC,SAAS,CAAC,CAAC;IAClB,CAAC;AACH,CAAC,CAAC;AA9EW,QAAA,oBAAoB,wBA8E/B;AAGK,MAAM,WAAW,GAAG,CAAC,YAAsB,EAAE,EAAE;IACpD,OAAO,CAAC,MAA2B,EAAE,IAAmC,EAAE,EAAE;QAC1E,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAChD,MAAM,KAAK,GAAkB,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YAC5D,KAAK,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,mBAAmB,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;YACnF,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5C,MAAM,KAAK,GAAkB,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YACnE,KAAK,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,0BAA0B,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC;YACvG,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAhBW,QAAA,WAAW,eAgBtB;AAGK,MAAM,iBAAiB,GAAG,CAAC,cAAsB,EAAE,EAAE;IAC1D,OAAO,CAAC,MAA2B,EAAE,IAAmC,EAAE,EAAE;QAC1E,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAChD,MAAM,KAAK,GAAkB,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YAC5D,KAAK,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,mBAAmB,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;YACnF,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,KAAK,cAAc,EAAE,CAAC;YACvC,MAAM,KAAK,GAAkB,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC/D,KAAK,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,sBAAsB,EAAE,OAAO,EAAE,2CAA2C,EAAE,CAAC;YACpG,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAhBW,QAAA,iBAAiB,qBAgB5B;AAGK,MAAM,iBAAiB,GAAG,CAAI,SAAmC,EAAE,EAAE;IAC1E,OAAO,CAAC,IAAS,EAAE,MAA2B,EAAE,IAAmC,EAAE,EAAE;QACrF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,MAAM,KAAK,GAAkB,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;YAC7D,KAAK,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;YAC/E,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QACD,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AATW,QAAA,iBAAiB,qBAS5B;AAGF,MAAM,WAAW,GAAG,IAAI,GAAG,EAAgD,CAAC;AAErE,MAAM,eAAe,GAAG,CAAC,YAAoB,GAAG,EAAE,WAAmB,KAAK,EAAE,EAAE;IACnF,OAAO,CAAC,MAA2B,EAAE,IAAmC,EAAE,EAAE;QAC1E,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAExC,IAAI,CAAC,UAAU,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;YAC9C,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC;YAC9D,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,IAAI,UAAU,CAAC,KAAK,IAAI,SAAS,EAAE,CAAC;YAClC,MAAM,KAAK,GAAkB,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YAC9D,KAAK,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,qBAAqB,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;YAC9E,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,UAAU,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AApBW,QAAA,eAAe,mBAoB1B;AAGF,WAAW,CAAC,GAAG,EAAE;IACf,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;QAChD,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YACzB,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;AACH,CAAC,EAAE,KAAK,CAAC,CAAC;AAGH,MAAM,eAAe,GAAG,CAAC,MAAc,EAAE,KAAkB,EAAE,EAAE;IACpE,MAAM,CAAC,IAAI,CAAC,sBAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACxC,sBAAM,CAAC,IAAI,CAAC,2BAA2B,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AAC9D,CAAC,CAAC;AAHW,QAAA,eAAe,mBAG1B;AAGK,MAAM,mBAAmB,GAAG,CAAC,MAAc,EAAE,OAAe,EAAE,OAAa,EAAE,EAAE;IACpF,MAAM,KAAK,GAAgB;QACzB,IAAI,EAAE,kBAAkB;QACxB,OAAO;QACP,OAAO;KACR,CAAC;IACF,MAAM,CAAC,IAAI,CAAC,sBAAa,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACnD,sBAAM,CAAC,IAAI,CAAC,+BAA+B,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AAClE,CAAC,CAAC;AARW,QAAA,mBAAmB,uBAQ9B;AAGK,MAAM,iBAAiB,GAAG,CAAC,SAAiB,EAAE,EAAE;IACrD,OAAO,CAAC,MAA2B,EAAE,IAAS,EAAE,IAAmC,EAAE,EAAE;QACrF,sBAAM,CAAC,IAAI,CAAC,gBAAgB,SAAS,cAAc,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,GAAG,EAAE;YACjF,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,SAAS;YACT,QAAQ,EAAE,MAAM,CAAC,EAAE;YACnB,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;SACxC,CAAC,CAAC;QACH,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAXW,QAAA,iBAAiB,qBAW5B"}