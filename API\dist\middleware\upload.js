"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleUploadError = exports.uploadPostImages = exports.uploadPostImage = exports.uploadProfilePicture = exports.uploadFields = exports.uploadMultiple = exports.uploadSingle = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const config_1 = require("../config/config");
const errorHandler_1 = require("./errorHandler");
const uploadDir = path_1.default.resolve(__dirname, '../../', config_1.config.upload.uploadPath);
if (!fs_1.default.existsSync(uploadDir)) {
    fs_1.default.mkdirSync(uploadDir, { recursive: true });
}
const createSubDir = (subPath) => {
    const fullPath = path_1.default.join(uploadDir, subPath);
    if (!fs_1.default.existsSync(fullPath)) {
        fs_1.default.mkdirSync(fullPath, { recursive: true });
    }
    return fullPath;
};
createSubDir('profiles');
createSubDir('posts');
createSubDir('temp');
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        let subDir = 'temp';
        if (req.route?.path?.includes('profile') || file.fieldname === 'profilePicture') {
            subDir = 'profiles';
        }
        else if (req.route?.path?.includes('post') || file.fieldname === 'postImage') {
            subDir = 'posts';
        }
        const destinationPath = path_1.default.join(uploadDir, subDir);
        cb(null, destinationPath);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const fileExtension = path_1.default.extname(file.originalname).toLowerCase();
        const fileName = `${file.fieldname}-${uniqueSuffix}${fileExtension}`;
        cb(null, fileName);
    }
});
const fileFilter = (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    }
    else {
        cb((0, errorHandler_1.createError)('Only image files (JPEG, PNG, GIF, WebP) are allowed', 400));
    }
};
const upload = (0, multer_1.default)({
    storage: storage,
    limits: {
        fileSize: config_1.config.upload.maxFileSize,
        files: 5,
    },
    fileFilter: fileFilter,
});
const uploadSingle = (fieldName) => upload.single(fieldName);
exports.uploadSingle = uploadSingle;
const uploadMultiple = (fieldName, maxCount = 5) => upload.array(fieldName, maxCount);
exports.uploadMultiple = uploadMultiple;
const uploadFields = (fields) => upload.fields(fields);
exports.uploadFields = uploadFields;
exports.uploadProfilePicture = (0, exports.uploadSingle)('profilePicture');
exports.uploadPostImage = (0, exports.uploadSingle)('postImage');
exports.uploadPostImages = (0, exports.uploadMultiple)('postImages', 3);
const handleUploadError = (error, req, res, next) => {
    if (error instanceof multer_1.default.MulterError) {
        switch (error.code) {
            case 'LIMIT_FILE_SIZE':
                return res.status(400).json({
                    error: 'File too large',
                    message: `File size must be less than ${config_1.config.upload.maxFileSize / (1024 * 1024)}MB`,
                });
            case 'LIMIT_FILE_COUNT':
                return res.status(400).json({
                    error: 'Too many files',
                    message: 'Maximum number of files exceeded',
                });
            case 'LIMIT_UNEXPECTED_FILE':
                return res.status(400).json({
                    error: 'Unexpected file field',
                    message: 'Unexpected file field in the request',
                });
            default:
                return res.status(400).json({
                    error: 'Upload error',
                    message: error.message,
                });
        }
    }
    next(error);
};
exports.handleUploadError = handleUploadError;
exports.default = upload;
//# sourceMappingURL=upload.js.map