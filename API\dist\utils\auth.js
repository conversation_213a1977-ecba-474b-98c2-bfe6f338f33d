"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthUtils = void 0;
const jwt = __importStar(require("jsonwebtoken"));
const bcrypt = __importStar(require("bcryptjs"));
class AuthUtils {
    static async hashPassword(password) {
        return bcrypt.hash(password, this.BCRYPT_ROUNDS);
    }
    static async comparePassword(password, hash) {
        return bcrypt.compare(password, hash);
    }
    static generateAccessToken(payload) {
        return jwt.sign(payload, this.JWT_SECRET, {
            expiresIn: this.JWT_EXPIRES_IN,
            issuer: "alumni-portal",
            audience: "alumni-portal-users",
        });
    }
    static generateRefreshToken(payload) {
        return jwt.sign(payload, this.JWT_REFRESH_SECRET, {
            expiresIn: this.JWT_REFRESH_EXPIRES_IN,
            issuer: "alumni-portal",
            audience: "alumni-portal-users",
        });
    }
    static generateTokenPair(user) {
        const payload = {
            userId: user.id.toString(),
            email: user.email,
            role: user.role,
            status: user.account_status || user.status,
        };
        return {
            accessToken: this.generateAccessToken(payload),
            refreshToken: this.generateRefreshToken(payload),
        };
    }
    static verifyAccessToken(token) {
        return jwt.verify(token, this.JWT_SECRET, {
            issuer: "alumni-portal",
            audience: "alumni-portal-users",
        });
    }
    static verifyRefreshToken(token) {
        return jwt.verify(token, this.JWT_REFRESH_SECRET, {
            issuer: "alumni-portal",
            audience: "alumni-portal-users",
        });
    }
    static extractTokenFromHeader(authHeader) {
        if (!authHeader || !authHeader.startsWith("Bearer ")) {
            return null;
        }
        return authHeader.substring(7);
    }
    static generateRandomPassword(length = 12) {
        const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
        let password = "";
        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        return password;
    }
    static validatePassword(password) {
        const errors = [];
        if (password.length < 8) {
            errors.push("Password must be at least 8 characters long");
        }
        if (!/[a-z]/.test(password)) {
            errors.push("Password must contain at least one lowercase letter");
        }
        if (!/[A-Z]/.test(password)) {
            errors.push("Password must contain at least one uppercase letter");
        }
        if (!/\d/.test(password)) {
            errors.push("Password must contain at least one number");
        }
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push("Password must contain at least one special character");
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    static validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    static validateUSN(usn) {
        const usnRegex = /^[A-Z]{2,4}\d{4}\d{3}$/;
        return usnRegex.test(usn);
    }
    static verifySessionRefreshToken(token) {
        try {
            return jwt.verify(token, this.JWT_REFRESH_SECRET);
        }
        catch (error) {
            return null;
        }
    }
    static generateSessionAccessToken(payload) {
        return jwt.sign(payload, this.JWT_SECRET, {
            expiresIn: process.env.ACCESS_TOKEN_EXPIRES_IN || "15m",
            issuer: "alumni-portal",
            audience: "alumni-portal-users",
        });
    }
    static generateSessionRefreshToken(payload) {
        return jwt.sign(payload, this.JWT_REFRESH_SECRET, {
            expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || "7d",
            issuer: "alumni-portal",
            audience: "alumni-portal-users",
        });
    }
}
exports.AuthUtils = AuthUtils;
AuthUtils.JWT_SECRET = process.env.JWT_SECRET;
AuthUtils.JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET;
AuthUtils.JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "7d";
AuthUtils.JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || "30d";
AuthUtils.BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_SALT_ROUNDS || "12");
(() => {
    if (!process.env.JWT_SECRET) {
        throw new Error("JWT_SECRET environment variable is required");
    }
    if (!process.env.JWT_REFRESH_SECRET) {
        throw new Error("JWT_REFRESH_SECRET environment variable is required");
    }
})();
//# sourceMappingURL=auth.js.map